package com.chatroom.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;

/**
 * 发送消息请求DTO
 */
public class SendMessageRequest {

    @NotNull(message = "房间ID不能为空")
    private Long roomId;

    @NotBlank(message = "消息内容不能为空")
    @Size(max = 2000, message = "消息内容长度不能超过2000个字符")
    private String content;

    @NotBlank(message = "消息类型不能为空")
    private String type = "TEXT"; // TEXT, IMAGE, FILE, SYSTEM

    private Long replyToId; // 回复的消息ID

    private String fileUrl; // 文件URL（当type为IMAGE或FILE时）
    private String fileName; // 文件名
    private Long fileSize; // 文件大小

    public SendMessageRequest() {}

    public SendMessageRequest(Long roomId, String content, String type) {
        this.roomId = roomId;
        this.content = content;
        this.type = type;
    }

    // Getters and Setters
    public Long getRoomId() {
        return roomId;
    }

    public void setRoomId(Long roomId) {
        this.roomId = roomId;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Long getReplyToId() {
        return replyToId;
    }

    public void setReplyToId(Long replyToId) {
        this.replyToId = replyToId;
    }

    public String getFileUrl() {
        return fileUrl;
    }

    public void setFileUrl(String fileUrl) {
        this.fileUrl = fileUrl;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    @Override
    public String toString() {
        return "SendMessageRequest{" +
                "roomId=" + roomId +
                ", content='" + content + '\'' +
                ", type='" + type + '\'' +
                ", replyToId=" + replyToId +
                ", fileUrl='" + fileUrl + '\'' +
                ", fileName='" + fileName + '\'' +
                ", fileSize=" + fileSize +
                '}';
    }
}
