package com.chatroom.controller;

import com.chatroom.dto.ChatMessageDto;
import com.chatroom.dto.UserStatusDto;
import com.chatroom.entity.Message;
import com.chatroom.entity.User;
import com.chatroom.service.MessageService;
import com.chatroom.service.UserService;
import com.chatroom.service.ChatRoomService;
import com.chatroom.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.handler.annotation.DestinationVariable;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.handler.annotation.SendTo;
import org.springframework.messaging.simp.SimpMessageHeaderAccessor;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Controller;

import java.security.Principal;
import java.time.LocalDateTime;
import java.util.Optional;

/**
 * WebSocket消息控制器
 */
@Controller
public class WebSocketController {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketController.class);

    @Autowired
    private SimpMessagingTemplate messagingTemplate;

    @Autowired
    private MessageService messageService;

    @Autowired
    private UserService userService;

    @Autowired
    private ChatRoomService chatRoomService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 发送聊天消息
     */
    @MessageMapping("/chat.sendMessage/{roomId}")
    public void sendMessage(@DestinationVariable Long roomId, 
                           @Payload ChatMessageDto chatMessage,
                           SimpMessageHeaderAccessor headerAccessor) {
        try {
            logger.info("收到聊天消息: roomId={}, content={}", roomId, chatMessage.getContent());

            // 获取用户信息
            String username = (String) headerAccessor.getSessionAttributes().get("username");
            if (username == null) {
                logger.error("用户未认证");
                return;
            }

            Optional<User> userOpt = userService.findByUsername(username);
            if (!userOpt.isPresent()) {
                logger.error("用户不存在: {}", username);
                return;
            }

            User user = userOpt.get();

            // 验证用户是否有权限发送消息到该房间
            if (!chatRoomService.isUserMember(roomId, user.getId())) {
                logger.error("用户 {} 不是房间 {} 的成员", username, roomId);
                return;
            }

            // 保存消息到数据库
            Message savedMessage = messageService.saveMessage(roomId, user.getId(), 
                chatMessage.getContent(), chatMessage.getType(), chatMessage.getReplyToId());

            // 构造响应消息
            ChatMessageDto responseMessage = new ChatMessageDto();
            responseMessage.setId(savedMessage.getId());
            responseMessage.setRoomId(roomId);
            responseMessage.setContent(savedMessage.getContent());
            responseMessage.setType(savedMessage.getType().name());
            responseMessage.setSenderId(user.getId());
            responseMessage.setSenderName(user.getNickname() != null ? user.getNickname() : user.getUsername());
            responseMessage.setSenderAvatar(user.getAvatarUrl());
            responseMessage.setTimestamp(savedMessage.getCreatedAt());
            responseMessage.setReplyToId(savedMessage.getReplyTo() != null ? savedMessage.getReplyTo().getId() : null);

            // 广播消息到房间内所有用户
            messagingTemplate.convertAndSend("/topic/room/" + roomId, responseMessage);

            logger.info("消息发送成功: messageId={}", savedMessage.getId());

        } catch (Exception e) {
            logger.error("发送消息失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 用户加入房间
     */
    @MessageMapping("/chat.addUser/{roomId}")
    public void addUser(@DestinationVariable Long roomId,
                       @Payload UserStatusDto userStatus,
                       SimpMessageHeaderAccessor headerAccessor) {
        try {
            String username = userStatus.getUsername();
            logger.info("用户加入房间: username={}, roomId={}", username, roomId);

            // 将用户名存储到WebSocket会话中
            headerAccessor.getSessionAttributes().put("username", username);
            headerAccessor.getSessionAttributes().put("roomId", roomId);

            Optional<User> userOpt = userService.findByUsername(username);
            if (!userOpt.isPresent()) {
                logger.error("用户不存在: {}", username);
                return;
            }

            User user = userOpt.get();

            // 更新用户在线状态
            userService.updateUserStatus(user.getId(), com.chatroom.enums.UserStatus.ONLINE);

            // 构造用户状态消息
            UserStatusDto statusMessage = new UserStatusDto();
            statusMessage.setUserId(user.getId());
            statusMessage.setUsername(username);
            statusMessage.setNickname(user.getNickname() != null ? user.getNickname() : username);
            statusMessage.setAvatarUrl(user.getAvatarUrl());
            statusMessage.setStatus("ONLINE");
            statusMessage.setType("JOIN");

            // 广播用户加入消息
            messagingTemplate.convertAndSend("/topic/room/" + roomId + "/users", statusMessage);

            logger.info("用户加入房间成功: username={}, roomId={}", username, roomId);

        } catch (Exception e) {
            logger.error("用户加入房间失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 用户正在输入
     */
    @MessageMapping("/chat.typing/{roomId}")
    public void userTyping(@DestinationVariable Long roomId,
                          @Payload UserStatusDto userStatus) {
        try {
            logger.debug("用户正在输入: username={}, roomId={}", userStatus.getUsername(), roomId);

            userStatus.setType("TYPING");
            messagingTemplate.convertAndSend("/topic/room/" + roomId + "/typing", userStatus);

        } catch (Exception e) {
            logger.error("处理用户输入状态失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 用户停止输入
     */
    @MessageMapping("/chat.stopTyping/{roomId}")
    public void userStopTyping(@DestinationVariable Long roomId,
                              @Payload UserStatusDto userStatus) {
        try {
            logger.debug("用户停止输入: username={}, roomId={}", userStatus.getUsername(), roomId);

            userStatus.setType("STOP_TYPING");
            messagingTemplate.convertAndSend("/topic/room/" + roomId + "/typing", userStatus);

        } catch (Exception e) {
            logger.error("处理用户停止输入状态失败: {}", e.getMessage(), e);
        }
    }
}
