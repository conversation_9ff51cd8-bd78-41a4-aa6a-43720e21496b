package com.chatroom.repository;

import com.chatroom.entity.ChatRoom;
import com.chatroom.entity.Message;
import com.chatroom.entity.User;
import com.chatroom.enums.MessageType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 消息数据访问接口
 */
@Repository
public interface MessageRepository extends JpaRepository<Message, Long> {

    /**
     * 根据房间查找消息（分页）
     */
    Page<Message> findByRoomAndIsDeletedFalseOrderByCreatedAtDesc(ChatRoom room, Pageable pageable);

    /**
     * 根据房间ID查找消息（分页）
     */
    @Query("SELECT m FROM Message m WHERE m.room.id = :roomId AND m.isDeleted = false ORDER BY m.createdAt DESC")
    Page<Message> findByRoomId(@Param("roomId") Long roomId, Pageable pageable);

    /**
     * 查找房间最近的消息
     */
    @Query("SELECT m FROM Message m WHERE m.room.id = :roomId AND m.isDeleted = false ORDER BY m.createdAt DESC")
    List<Message> findRecentMessages(@Param("roomId") Long roomId, Pageable pageable);

    /**
     * 根据发送者查找消息
     */
    Page<Message> findBySenderAndIsDeletedFalseOrderByCreatedAtDesc(User sender, Pageable pageable);

    /**
     * 根据消息类型查找消息
     */
    Page<Message> findByRoomAndTypeAndIsDeletedFalseOrderByCreatedAtDesc(ChatRoom room, MessageType type, Pageable pageable);

    /**
     * 搜索消息内容
     */
    @Query("SELECT m FROM Message m WHERE m.room.id = :roomId AND m.content LIKE %:keyword% AND m.isDeleted = false ORDER BY m.createdAt DESC")
    Page<Message> searchMessages(@Param("roomId") Long roomId, @Param("keyword") String keyword, Pageable pageable);

    /**
     * 查找指定时间范围内的消息
     */
    @Query("SELECT m FROM Message m WHERE m.room.id = :roomId AND m.createdAt BETWEEN :startTime AND :endTime AND m.isDeleted = false ORDER BY m.createdAt DESC")
    List<Message> findMessagesByTimeRange(@Param("roomId") Long roomId, 
                                         @Param("startTime") LocalDateTime startTime, 
                                         @Param("endTime") LocalDateTime endTime);

    /**
     * 统计房间消息数量
     */
    @Query("SELECT COUNT(m) FROM Message m WHERE m.room.id = :roomId AND m.isDeleted = false")
    long countRoomMessages(@Param("roomId") Long roomId);

    /**
     * 统计用户发送的消息数量
     */
    @Query("SELECT COUNT(m) FROM Message m WHERE m.sender.id = :userId AND m.isDeleted = false")
    long countUserMessages(@Param("userId") Long userId);

    /**
     * 查找用户在指定房间的最后一条消息
     */
    @Query("SELECT m FROM Message m WHERE m.room.id = :roomId AND m.sender.id = :userId AND m.isDeleted = false ORDER BY m.createdAt DESC")
    List<Message> findUserLastMessageInRoom(@Param("roomId") Long roomId, @Param("userId") Long userId, Pageable pageable);

    /**
     * 软删除消息
     */
    @Modifying
    @Query("UPDATE Message m SET m.isDeleted = true, m.deletedAt = :deletedAt WHERE m.id = :messageId")
    int softDeleteMessage(@Param("messageId") Long messageId, @Param("deletedAt") LocalDateTime deletedAt);

    /**
     * 查找可以撤回的消息
     */
    @Query("SELECT m FROM Message m WHERE m.id = :messageId AND m.sender.id = :userId AND m.isDeleted = false AND m.createdAt > :timeLimit")
    List<Message> findRecallableMessage(@Param("messageId") Long messageId, 
                                       @Param("userId") Long userId, 
                                       @Param("timeLimit") LocalDateTime timeLimit);

    /**
     * 查找回复某条消息的所有消息
     */
    @Query("SELECT m FROM Message m WHERE m.replyTo.id = :messageId AND m.isDeleted = false ORDER BY m.createdAt ASC")
    List<Message> findReplies(@Param("messageId") Long messageId);

    /**
     * 统计今日消息数量
     */
    @Query("SELECT COUNT(m) FROM Message m WHERE DATE(m.createdAt) = CURRENT_DATE AND m.isDeleted = false")
    long countTodayMessages();

    /**
     * 查找热门房间（按消息数量排序）
     */
    @Query("SELECT m.room.id, COUNT(m) as messageCount FROM Message m WHERE m.createdAt > :since AND m.isDeleted = false GROUP BY m.room.id ORDER BY messageCount DESC")
    List<Object[]> findActiveRoomsByMessageCount(@Param("since") LocalDateTime since, Pageable pageable);
}
