import React, { useState, useEffect } from 'react';
import { 
  UserIcon,
  ChatBubbleLeftIcon,
  MagnifyingGlassIcon
} from '@heroicons/react/24/outline';
import { User } from '@/types';
import api, { userAPI, roomAPI } from '@/services/api';
import { toast } from 'react-hot-toast';

interface UserListProps {
  roomId: number;
  onlineUsers: User[];
  currentUser: User;
}

interface UserItemProps {
  user: User;
  currentUser: User;
  onStartDirectChat: (userId: number) => void;
}

const UserItem: React.FC<UserItemProps> = ({ user, currentUser, onStartDirectChat }) => {
  const isCurrentUser = user.id === currentUser.id;
  
  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ONLINE':
        return 'bg-green-400';
      case 'AWAY':
        return 'bg-yellow-400';
      case 'BUSY':
        return 'bg-red-400';
      default:
        return 'bg-gray-400';
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'ONLINE':
        return '在线';
      case 'AWAY':
        return '离开';
      case 'BUSY':
        return '忙碌';
      default:
        return '离线';
    }
  };

  return (
    <div className="flex items-center justify-between p-3 hover:bg-gray-100 rounded-lg transition-colors">
      <div className="flex items-center space-x-3 flex-1 min-w-0">
        {/* 头像 */}
        <div className="relative flex-shrink-0">
          <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
            {user.avatarUrl ? (
              <img
                src={user.avatarUrl}
                alt={user.nickname || user.username}
                className="w-8 h-8 rounded-full object-cover"
              />
            ) : (
              <UserIcon className="h-5 w-5 text-gray-600" />
            )}
          </div>
          {/* 状态指示器 */}
          <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(user.status)}`}></div>
        </div>

        {/* 用户信息 */}
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2">
            <p className="text-sm font-medium text-gray-900 truncate">
              {user.nickname || user.username}
              {isCurrentUser && <span className="text-gray-500 ml-1">(我)</span>}
            </p>
          </div>
          <p className="text-xs text-gray-500">
            {getStatusText(user.status)}
          </p>
          {user.signature && (
            <p className="text-xs text-gray-400 truncate mt-0.5">
              {user.signature}
            </p>
          )}
        </div>
      </div>

      {/* 操作按钮 */}
      {!isCurrentUser && (
        <div className="flex items-center space-x-1">
          <button
            onClick={() => onStartDirectChat(user.id)}
            className="p-1.5 text-gray-400 hover:text-blue-600 hover:bg-blue-50 rounded transition-colors"
            title="私聊"
          >
            <ChatBubbleLeftIcon className="h-4 w-4" />
          </button>
        </div>
      )}
    </div>
  );
};

const UserList: React.FC<UserListProps> = ({ roomId, onlineUsers, currentUser }) => {
  const [users, setUsers] = useState<User[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);

  // 加载房间成员
  const loadRoomMembers = async () => {
    try {
      // 这里应该有一个获取房间成员的API，暂时使用在线用户
      const onlineUsersList = await userAPI.getOnlineUsers();
      setUsers(onlineUsersList);
      setIsLoading(false);
    } catch (error) {
      console.error('加载房间成员失败:', error);
      setIsLoading(false);
    }
  };

  // 开始私聊
  const handleStartDirectChat = async (targetUserId: number) => {
    try {
      const directRoom = await roomAPI.createDirectChat(targetUserId);
      // 跳转到私聊房间
      window.location.href = `/chat/room/${directRoom.id}`;
    } catch (error) {
      console.error('创建私聊失败:', error);
      toast.error('创建私聊失败');
    }
  };

  // 过滤用户
  const filteredUsers = users.filter(user => {
    const searchLower = searchTerm.toLowerCase();
    return (
      user.username.toLowerCase().includes(searchLower) ||
      (user.nickname && user.nickname.toLowerCase().includes(searchLower))
    );
  });

  // 按状态排序用户
  const sortedUsers = filteredUsers.sort((a, b) => {
    // 当前用户排在最前面
    if (a.id === currentUser.id) return -1;
    if (b.id === currentUser.id) return 1;
    
    // 按状态排序：在线 > 离开 > 忙碌 > 离线
    const statusOrder = { 'ONLINE': 0, 'AWAY': 1, 'BUSY': 2, 'OFFLINE': 3 };
    const aOrder = statusOrder[a.status as keyof typeof statusOrder] ?? 3;
    const bOrder = statusOrder[b.status as keyof typeof statusOrder] ?? 3;
    
    if (aOrder !== bOrder) {
      return aOrder - bOrder;
    }
    
    // 状态相同时按昵称排序
    const aName = a.nickname || a.username;
    const bName = b.nickname || b.username;
    return aName.localeCompare(bName);
  });

  useEffect(() => {
    loadRoomMembers();
    
    // 定期刷新在线用户列表
    const interval = setInterval(loadRoomMembers, 30000); // 30秒刷新一次
    
    return () => clearInterval(interval);
  }, [roomId]);

  return (
    <div className="h-full flex flex-col">
      {/* 标题 */}
      <div className="p-4 border-b border-gray-200">
        <h3 className="text-lg font-medium text-gray-900">
          成员列表
          <span className="ml-2 text-sm text-gray-500">
            ({users.length})
          </span>
        </h3>
      </div>

      {/* 搜索框 */}
      <div className="p-4 border-b border-gray-200">
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="搜索成员..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* 用户列表 */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-600"></div>
          </div>
        ) : sortedUsers.length === 0 ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <UserIcon className="h-8 w-8 text-gray-400 mx-auto mb-2" />
              <p className="text-sm text-gray-500">
                {searchTerm ? '未找到匹配的成员' : '暂无成员'}
              </p>
            </div>
          </div>
        ) : (
          <div className="p-2 space-y-1">
            {sortedUsers.map(user => (
              <UserItem
                key={user.id}
                user={user}
                currentUser={currentUser}
                onStartDirectChat={handleStartDirectChat}
              />
            ))}
          </div>
        )}
      </div>

      {/* 在线状态统计 */}
      <div className="p-4 border-t border-gray-200 bg-gray-50">
        <div className="flex items-center justify-between text-sm">
          <span className="text-gray-600">在线状态</span>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-green-400 rounded-full"></div>
              <span className="text-gray-600">
                {users.filter(u => u.status === 'ONLINE').length}
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-yellow-400 rounded-full"></div>
              <span className="text-gray-600">
                {users.filter(u => u.status === 'AWAY').length}
              </span>
            </div>
            <div className="flex items-center space-x-1">
              <div className="w-2 h-2 bg-red-400 rounded-full"></div>
              <span className="text-gray-600">
                {users.filter(u => u.status === 'BUSY').length}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UserList;
