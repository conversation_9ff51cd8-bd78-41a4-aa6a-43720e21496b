import React, { useState, useEffect } from 'react';
import { Outlet, useNavigate, useLocation } from 'react-router-dom';
import { 
  ChatBubbleLeftRightIcon,
  UserGroupIcon,
  Cog6ToothIcon,
  ArrowRightOnRectangleIcon,
  UserIcon
} from '@heroicons/react/24/outline';
import { User } from '@/types';
import { authAPI, userAPI } from '@/services/api';
import { toast } from 'react-hot-toast';
import websocketService from '@/services/websocket';

interface ChatLayoutProps {
  currentUser: User;
  onLogout: () => void;
}

const ChatLayout: React.FC<ChatLayoutProps> = ({ currentUser, onLogout }) => {
  const navigate = useNavigate();
  const location = useLocation();
  const [user, setUser] = useState<User>(currentUser);
  const [showUserMenu, setShowUserMenu] = useState(false);

  // 导航菜单项
  const navigationItems = [
    {
      name: '聊天室',
      href: '/chat',
      icon: ChatBubbleLeftRightIcon,
      current: location.pathname === '/chat'
    },
    {
      name: '房间列表',
      href: '/chat/rooms',
      icon: UserGroupIcon,
      current: location.pathname === '/chat/rooms'
    }
  ];

  // 更新用户状态
  const updateUserStatus = async (status: 'ONLINE' | 'AWAY' | 'BUSY' | 'OFFLINE') => {
    try {
      await userAPI.updateUserStatus(status);
      setUser(prev => ({ ...prev, status }));
      toast.success(`状态已更新为${getStatusText(status)}`);
    } catch (error) {
      console.error('更新用户状态失败:', error);
      toast.error('更新状态失败');
    }
  };

  // 获取状态文本
  const getStatusText = (status: string) => {
    switch (status) {
      case 'ONLINE':
        return '在线';
      case 'AWAY':
        return '离开';
      case 'BUSY':
        return '忙碌';
      case 'OFFLINE':
        return '离线';
      default:
        return '未知';
    }
  };

  // 获取状态颜色
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ONLINE':
        return 'bg-green-400';
      case 'AWAY':
        return 'bg-yellow-400';
      case 'BUSY':
        return 'bg-red-400';
      case 'OFFLINE':
        return 'bg-gray-400';
      default:
        return 'bg-gray-400';
    }
  };

  // 处理登出
  const handleLogout = async () => {
    try {
      // 更新状态为离线
      await updateUserStatus('OFFLINE');
      
      // 断开WebSocket连接
      websocketService.disconnect();
      
      // 调用登出API
      await authAPI.logout();
      
      // 清除本地存储
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      
      // 调用父组件的登出回调
      onLogout();
      
      toast.success('已退出登录');
    } catch (error) {
      console.error('登出失败:', error);
      // 即使API调用失败，也要清除本地状态
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      onLogout();
    }
  };

  // 页面可见性变化处理
  const handleVisibilityChange = () => {
    if (document.hidden) {
      // 页面隐藏时设置为离开状态
      updateUserStatus('AWAY');
    } else {
      // 页面显示时设置为在线状态
      updateUserStatus('ONLINE');
    }
  };

  // 窗口关闭前处理
  const handleBeforeUnload = () => {
    updateUserStatus('OFFLINE');
    websocketService.disconnect();
  };

  useEffect(() => {
    // 初始化时设置为在线状态
    updateUserStatus('ONLINE');

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', handleVisibilityChange);
    
    // 监听窗口关闭事件
    window.addEventListener('beforeunload', handleBeforeUnload);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, []);

  return (
    <div className="h-screen flex bg-gray-100">
      {/* 侧边栏 */}
      <div className="w-64 bg-white shadow-lg flex flex-col">
        {/* 用户信息 */}
        <div className="p-4 border-b border-gray-200">
          <div className="relative">
            <button
              onClick={() => setShowUserMenu(!showUserMenu)}
              className="w-full flex items-center space-x-3 p-2 rounded-lg hover:bg-gray-50 transition-colors"
            >
              {/* 头像 */}
              <div className="relative flex-shrink-0">
                <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
                  {user.avatarUrl ? (
                    <img
                      src={user.avatarUrl}
                      alt={user.nickname || user.username}
                      className="w-10 h-10 rounded-full object-cover"
                    />
                  ) : (
                    <UserIcon className="h-6 w-6 text-gray-600" />
                  )}
                </div>
                {/* 状态指示器 */}
                <div className={`absolute -bottom-0.5 -right-0.5 w-3 h-3 rounded-full border-2 border-white ${getStatusColor(user.status)}`}></div>
              </div>

              {/* 用户信息 */}
              <div className="flex-1 min-w-0 text-left">
                <p className="text-sm font-medium text-gray-900 truncate">
                  {user.nickname || user.username}
                </p>
                <p className="text-xs text-gray-500">
                  {getStatusText(user.status)}
                </p>
              </div>
            </button>

            {/* 用户菜单 */}
            {showUserMenu && (
              <div className="absolute top-full left-0 right-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-10">
                <div className="py-1">
                  {/* 状态选择 */}
                  <div className="px-3 py-2 text-xs font-medium text-gray-500 uppercase tracking-wide">
                    设置状态
                  </div>
                  {[
                    { status: 'ONLINE', text: '在线', color: 'bg-green-400' },
                    { status: 'AWAY', text: '离开', color: 'bg-yellow-400' },
                    { status: 'BUSY', text: '忙碌', color: 'bg-red-400' }
                  ].map(({ status, text, color }) => (
                    <button
                      key={status}
                      onClick={() => {
                        updateUserStatus(status as any);
                        setShowUserMenu(false);
                      }}
                      className="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                    >
                      <div className={`w-2 h-2 rounded-full ${color} mr-3`}></div>
                      {text}
                      {user.status === status && (
                        <span className="ml-auto text-blue-600">✓</span>
                      )}
                    </button>
                  ))}
                  
                  <div className="border-t border-gray-100 my-1"></div>
                  
                  {/* 设置 */}
                  <button
                    onClick={() => {
                      navigate('/profile');
                      setShowUserMenu(false);
                    }}
                    className="w-full flex items-center px-3 py-2 text-sm text-gray-700 hover:bg-gray-50"
                  >
                    <Cog6ToothIcon className="h-4 w-4 mr-3" />
                    个人设置
                  </button>
                  
                  {/* 登出 */}
                  <button
                    onClick={() => {
                      handleLogout();
                      setShowUserMenu(false);
                    }}
                    className="w-full flex items-center px-3 py-2 text-sm text-red-600 hover:bg-red-50"
                  >
                    <ArrowRightOnRectangleIcon className="h-4 w-4 mr-3" />
                    退出登录
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* 导航菜单 */}
        <nav className="flex-1 p-4 space-y-2">
          {navigationItems.map((item) => {
            const Icon = item.icon;
            return (
              <button
                key={item.name}
                onClick={() => navigate(item.href)}
                className={`w-full flex items-center px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                  item.current
                    ? 'bg-blue-100 text-blue-700'
                    : 'text-gray-600 hover:bg-gray-100 hover:text-gray-900'
                }`}
              >
                <Icon className="h-5 w-5 mr-3" />
                {item.name}
              </button>
            );
          })}
        </nav>

        {/* 版本信息 */}
        <div className="p-4 border-t border-gray-200">
          <p className="text-xs text-gray-500 text-center">
            聊天室 v1.0.0
          </p>
        </div>
      </div>

      {/* 主内容区域 */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <Outlet context={{ currentUser: user }} />
      </div>

      {/* 点击外部关闭用户菜单 */}
      {showUserMenu && (
        <div
          className="fixed inset-0 z-0"
          onClick={() => setShowUserMenu(false)}
        />
      )}
    </div>
  );
};

export default ChatLayout;
