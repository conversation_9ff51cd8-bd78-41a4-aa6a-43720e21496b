# Redis 缓存策略设计

## 缓存键命名规范

### 用户相关
- `user:session:{userId}` - 用户会话信息
- `user:online:{userId}` - 用户在线状态
- `user:profile:{userId}` - 用户基本信息缓存

### 聊天室相关
- `room:info:{roomId}` - 房间基本信息
- `room:members:{roomId}` - 房间成员列表
- `room:online:{roomId}` - 房间在线用户

### 消息相关
- `message:recent:{roomId}` - 房间最近消息(最近50条)
- `message:unread:{userId}:{roomId}` - 用户未读消息数
- `message:typing:{roomId}` - 正在输入状态

### 系统配置
- `config:system` - 系统配置信息
- `config:sensitive_words` - 敏感词列表

## 缓存过期策略

| 缓存类型 | 过期时间 | 说明 |
|---------|---------|------|
| 用户会话 | 7天 | JWT Token有效期 |
| 用户在线状态 | 5分钟 | 定期更新心跳 |
| 用户基本信息 | 1小时 | 减少数据库查询 |
| 房间信息 | 30分钟 | 房间信息变化较少 |
| 房间成员列表 | 10分钟 | 成员变化时主动更新 |
| 最近消息 | 1小时 | 新消息时主动更新 |
| 未读消息数 | 无过期 | 读取消息时清除 |
| 正在输入状态 | 10秒 | 短暂状态 |
| 系统配置 | 1天 | 配置变化较少 |

## 数据结构设计

### 用户会话 (Hash)
```
user:session:{userId}
- token: JWT令牌
- loginTime: 登录时间
- lastActiveTime: 最后活跃时间
- deviceInfo: 设备信息
```

### 房间在线用户 (Set)
```
room:online:{roomId}
- 存储在线用户ID集合
- 用于快速获取房间在线人数和用户列表
```

### 最近消息 (List)
```
message:recent:{roomId}
- 存储最近50条消息的JSON数据
- 新消息从左侧推入，超出长度从右侧弹出
```

### 正在输入状态 (Hash)
```
message:typing:{roomId}
- userId: 用户ID
- timestamp: 开始输入时间
- 定期清理过期数据
```

## 缓存更新策略

### 写入策略
1. **Write Through**: 同时更新数据库和缓存
2. **Write Behind**: 先更新缓存，异步更新数据库

### 失效策略
1. **主动失效**: 数据变更时主动删除相关缓存
2. **被动失效**: 设置合理的过期时间
3. **版本控制**: 使用版本号避免缓存不一致

### 预热策略
1. **应用启动时**: 预加载热点数据
2. **定时任务**: 定期刷新重要缓存
3. **用户访问时**: 懒加载用户相关数据

## 性能优化

### 批量操作
- 使用Pipeline减少网络往返
- 使用Lua脚本保证原子性操作

### 内存优化
- 合理设置过期时间
- 使用压缩算法减少内存占用
- 定期清理无效数据

### 监控指标
- 缓存命中率
- 内存使用率
- 响应时间
- 错误率
