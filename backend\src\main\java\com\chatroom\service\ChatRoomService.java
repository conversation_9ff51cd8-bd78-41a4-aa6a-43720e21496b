package com.chatroom.service;

import com.chatroom.entity.ChatRoom;
import com.chatroom.entity.User;
import com.chatroom.enums.RoomType;
import com.chatroom.repository.ChatRoomRepository;
import com.chatroom.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

/**
 * 聊天室服务类
 */
@Service
@Transactional
public class ChatRoomService {

    private static final Logger logger = LoggerFactory.getLogger(ChatRoomService.class);

    @Autowired
    private ChatRoomRepository chatRoomRepository;

    @Autowired
    private UserRepository userRepository;

    /**
     * 创建聊天室
     */
    public ChatRoom createRoom(String name, String description, RoomType type, Long ownerId, Integer maxMembers) {
        logger.info("创建聊天室: name={}, type={}, ownerId={}", name, type, ownerId);

        User owner = userRepository.findById(ownerId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        ChatRoom room = new ChatRoom();
        room.setName(name);
        room.setDescription(description);
        room.setType(type);
        room.setOwner(owner);
        room.setMaxMembers(maxMembers != null ? maxMembers : 100);
        room.setIsActive(true);

        // 房主自动加入房间
        room.addMember(owner);

        ChatRoom savedRoom = chatRoomRepository.save(room);
        logger.info("聊天室创建成功: roomId={}", savedRoom.getId());
        return savedRoom;
    }

    /**
     * 加入聊天室
     */
    public boolean joinRoom(Long roomId, Long userId) {
        logger.info("用户加入聊天室: roomId={}, userId={}", roomId, userId);

        Optional<ChatRoom> roomOpt = chatRoomRepository.findById(roomId);
        Optional<User> userOpt = userRepository.findById(userId);

        if (!roomOpt.isPresent() || !userOpt.isPresent()) {
            logger.error("聊天室或用户不存在: roomId={}, userId={}", roomId, userId);
            return false;
        }

        ChatRoom room = roomOpt.get();
        User user = userOpt.get();

        // 检查房间是否激活
        if (!room.getIsActive()) {
            logger.error("聊天室未激活: roomId={}", roomId);
            return false;
        }

        // 检查是否已经是成员
        if (isUserMember(roomId, userId)) {
            logger.info("用户已经是房间成员: roomId={}, userId={}", roomId, userId);
            return true;
        }

        // 检查房间人数限制
        if (room.getMemberCount() >= room.getMaxMembers()) {
            logger.error("聊天室人数已满: roomId={}, current={}, max={}", 
                        roomId, room.getMemberCount(), room.getMaxMembers());
            return false;
        }

        // 加入房间
        room.addMember(user);
        chatRoomRepository.save(room);

        logger.info("用户加入聊天室成功: roomId={}, userId={}", roomId, userId);
        return true;
    }

    /**
     * 离开聊天室
     */
    public boolean leaveRoom(Long roomId, Long userId) {
        logger.info("用户离开聊天室: roomId={}, userId={}", roomId, userId);

        Optional<ChatRoom> roomOpt = chatRoomRepository.findById(roomId);
        Optional<User> userOpt = userRepository.findById(userId);

        if (!roomOpt.isPresent() || !userOpt.isPresent()) {
            logger.error("聊天室或用户不存在: roomId={}, userId={}", roomId, userId);
            return false;
        }

        ChatRoom room = roomOpt.get();
        User user = userOpt.get();

        // 检查是否是房间成员
        if (!isUserMember(roomId, userId)) {
            logger.info("用户不是房间成员: roomId={}, userId={}", roomId, userId);
            return true;
        }

        // 房主不能离开自己的房间
        if (room.getOwner().getId().equals(userId)) {
            logger.error("房主不能离开自己的房间: roomId={}, userId={}", roomId, userId);
            return false;
        }

        // 离开房间
        room.removeMember(user);
        chatRoomRepository.save(room);

        logger.info("用户离开聊天室成功: roomId={}, userId={}", roomId, userId);
        return true;
    }

    /**
     * 检查用户是否是房间成员
     */
    @Transactional(readOnly = true)
    public boolean isUserMember(Long roomId, Long userId) {
        return chatRoomRepository.isUserMember(roomId, userId);
    }

    /**
     * 获取聊天室详情
     */
    @Transactional(readOnly = true)
    public Optional<ChatRoom> getRoomById(Long roomId) {
        return chatRoomRepository.findById(roomId);
    }

    /**
     * 获取公开聊天室列表
     */
    @Transactional(readOnly = true)
    public List<ChatRoom> getPublicRooms() {
        return chatRoomRepository.findPublicRooms();
    }

    /**
     * 获取用户参与的聊天室
     */
    @Transactional(readOnly = true)
    public List<ChatRoom> getUserRooms(Long userId) {
        return chatRoomRepository.findUserRooms(userId);
    }

    /**
     * 搜索公开聊天室
     */
    @Transactional(readOnly = true)
    public Page<ChatRoom> searchPublicRooms(String keyword, int page, int size) {
        Pageable pageable = PageRequest.of(page, size);
        return chatRoomRepository.searchPublicRoomsByName(keyword, pageable);
    }

    /**
     * 获取热门聊天室
     */
    @Transactional(readOnly = true)
    public List<ChatRoom> getPopularRooms(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return chatRoomRepository.findPopularRooms(pageable);
    }

    /**
     * 获取房间成员数量
     */
    @Transactional(readOnly = true)
    public int getRoomMemberCount(Long roomId) {
        return chatRoomRepository.getRoomMemberCount(roomId);
    }

    /**
     * 创建私聊房间
     */
    public ChatRoom createDirectChatRoom(Long userId1, Long userId2) {
        logger.info("创建私聊房间: userId1={}, userId2={}", userId1, userId2);

        // 检查是否已存在私聊房间
        Optional<ChatRoom> existingRoom = chatRoomRepository.findDirectChatRoom(userId1, userId2);
        if (existingRoom.isPresent()) {
            logger.info("私聊房间已存在: roomId={}", existingRoom.get().getId());
            return existingRoom.get();
        }

        User user1 = userRepository.findById(userId1)
                .orElseThrow(() -> new RuntimeException("用户1不存在"));
        User user2 = userRepository.findById(userId2)
                .orElseThrow(() -> new RuntimeException("用户2不存在"));

        // 创建私聊房间
        ChatRoom room = new ChatRoom();
        room.setName(user1.getNickname() + " & " + user2.getNickname());
        room.setDescription("私聊");
        room.setType(RoomType.DIRECT);
        room.setOwner(user1);
        room.setMaxMembers(2);
        room.setIsActive(true);

        // 添加两个用户到房间
        room.addMember(user1);
        room.addMember(user2);

        ChatRoom savedRoom = chatRoomRepository.save(room);
        logger.info("私聊房间创建成功: roomId={}", savedRoom.getId());
        return savedRoom;
    }

    /**
     * 统计聊天室数量
     */
    @Transactional(readOnly = true)
    public long countActiveRooms() {
        return chatRoomRepository.countActiveRooms();
    }

    /**
     * 根据类型统计聊天室数量
     */
    @Transactional(readOnly = true)
    public long countRoomsByType(RoomType type) {
        return chatRoomRepository.countRoomsByType(type);
    }
}
