version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: chatroom-mysql
    environment:
      MYSQL_ROOT_PASSWORD: root123
      MYSQL_DATABASE: chatroom
      MYSQL_USER: chatroom
      MYSQL_PASSWORD: chatroom123
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql
    networks:
      - chatroom-network

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: chatroom-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - chatroom-network

  # 后端服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: chatroom-backend
    environment:
      SPRING_PROFILES_ACTIVE: docker
      SPRING_DATASOURCE_URL: ********************************
      SPRING_DATASOURCE_USERNAME: chatroom
      SPRING_DATASOURCE_PASSWORD: chatroom123
      SPRING_REDIS_HOST: redis
      SPRING_REDIS_PORT: 6379
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
    networks:
      - chatroom-network

  # 前端应用
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: chatroom-frontend
    ports:
      - "80:80"
    depends_on:
      - backend
    networks:
      - chatroom-network

volumes:
  mysql_data:
  redis_data:

networks:
  chatroom-network:
    driver: bridge
