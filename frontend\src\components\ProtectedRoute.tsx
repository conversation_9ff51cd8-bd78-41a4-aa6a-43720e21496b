import React, { useEffect, useState } from 'react';
import { Navigate } from 'react-router-dom';
import { Spin } from 'antd';
import { useAuthStore } from '@/store/useAuthStore';
import { userAPI } from '@/services/api';
import { User } from '@/types';

interface ProtectedRouteProps {
  children: React.ReactNode;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ children }) => {
  const { isAuthenticated, token, isLoading, setLoading, logout } = useAuthStore();
  const [currentUser, setCurrentUser] = useState<User | null>(null);
  const [userLoading, setUserLoading] = useState(true);

  // 加载当前用户信息
  const loadCurrentUser = async () => {
    try {
      const user = await userAPI.getCurrentUser();
      setCurrentUser(user);
    } catch (error) {
      console.error('获取用户信息失败:', error);
      // 如果获取用户信息失败，可能token已过期，清除认证状态
      logout();
    } finally {
      setUserLoading(false);
    }
  };

  useEffect(() => {
    // 检查本地存储中的token
    const storedToken = localStorage.getItem('token');
    if (storedToken && !token) {
      // 如果有token但store中没有，可能需要验证token有效性
      setLoading(false);
    }
  }, [token, setLoading]);

  useEffect(() => {
    // 如果已认证或有本地token，加载用户信息
    if (isAuthenticated || localStorage.getItem('token')) {
      loadCurrentUser();
    } else {
      setUserLoading(false);
    }
  }, [isAuthenticated]);

  // 如果正在加载，显示加载动画
  if (isLoading || userLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Spin size="large" />
      </div>
    );
  }

  // 如果未认证，重定向到登录页
  if (!isAuthenticated && !localStorage.getItem('token')) {
    return <Navigate to="/login" replace />;
  }

  // 如果没有用户信息，重定向到登录页
  if (!currentUser) {
    return <Navigate to="/login" replace />;
  }

  // 克隆子组件并传递用户信息和登出函数
  return React.cloneElement(children as React.ReactElement, {
    currentUser,
    onLogout: logout
  });
};

export default ProtectedRoute;
