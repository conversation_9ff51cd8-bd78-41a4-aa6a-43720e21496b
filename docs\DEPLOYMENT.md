# 部署指南

## 部署方式

### 1. Docker Compose 部署（推荐）

#### 环境要求
- Docker 20.10+
- Docker Compose 2.0+
- 2GB+ RAM
- 10GB+ 磁盘空间

#### 部署步骤

1. **克隆项目**
```bash
git clone <repository-url>
cd chatroom
```

2. **配置环境变量**
```bash
cp docker/.env.example .env
# 编辑 .env 文件，修改数据库密码、JWT密钥等
```

3. **启动服务**
```bash
make start
# 或者
docker-compose up -d
```

4. **验证部署**
```bash
# 检查服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 访问地址
- 前端应用: http://localhost
- 后端API: http://localhost:8080
- API文档: http://localhost:8080/swagger-ui.html

### 2. 手动部署

#### 环境要求
- Java 17+
- Node.js 18+
- MySQL 8.0+
- Redis 6.0+
- Nginx

#### 后端部署

1. **构建应用**
```bash
cd backend
mvn clean package -DskipTests
```

2. **配置数据库**
```sql
CREATE DATABASE chatroom CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
CREATE USER 'chatroom'@'%' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON chatroom.* TO 'chatroom'@'%';
FLUSH PRIVILEGES;
```

3. **运行应用**
```bash
java -jar target/chatroom-backend-1.0.0.jar \
  --spring.profiles.active=prod \
  --spring.datasource.url=************************************ \
  --spring.datasource.username=chatroom \
  --spring.datasource.password=your_password
```

#### 前端部署

1. **构建应用**
```bash
cd frontend
npm install
npm run build
```

2. **配置Nginx**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /path/to/frontend/dist;
    index index.html;

    # API代理
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    }

    # WebSocket代理
    location /ws/ {
        proxy_pass http://localhost:8080/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # SPA路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

### 3. 云平台部署

#### AWS 部署

1. **使用 ECS + RDS + ElastiCache**
```bash
# 构建并推送镜像到ECR
aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin <account-id>.dkr.ecr.us-east-1.amazonaws.com

docker build -t chatroom-backend ./backend
docker tag chatroom-backend:latest <account-id>.dkr.ecr.us-east-1.amazonaws.com/chatroom-backend:latest
docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/chatroom-backend:latest

docker build -t chatroom-frontend ./frontend
docker tag chatroom-frontend:latest <account-id>.dkr.ecr.us-east-1.amazonaws.com/chatroom-frontend:latest
docker push <account-id>.dkr.ecr.us-east-1.amazonaws.com/chatroom-frontend:latest
```

2. **创建ECS任务定义**
```json
{
  "family": "chatroom",
  "networkMode": "awsvpc",
  "requiresCompatibilities": ["FARGATE"],
  "cpu": "512",
  "memory": "1024",
  "containerDefinitions": [
    {
      "name": "backend",
      "image": "<account-id>.dkr.ecr.us-east-1.amazonaws.com/chatroom-backend:latest",
      "portMappings": [
        {
          "containerPort": 8080,
          "protocol": "tcp"
        }
      ],
      "environment": [
        {
          "name": "SPRING_PROFILES_ACTIVE",
          "value": "prod"
        }
      ]
    }
  ]
}
```

#### Kubernetes 部署

1. **创建配置文件**
```yaml
# k8s/namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: chatroom

---
# k8s/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: chatroom-config
  namespace: chatroom
data:
  SPRING_PROFILES_ACTIVE: "prod"
  MYSQL_HOST: "mysql-service"
  REDIS_HOST: "redis-service"

---
# k8s/backend-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: chatroom-backend
  namespace: chatroom
spec:
  replicas: 2
  selector:
    matchLabels:
      app: chatroom-backend
  template:
    metadata:
      labels:
        app: chatroom-backend
    spec:
      containers:
      - name: backend
        image: chatroom-backend:latest
        ports:
        - containerPort: 8080
        envFrom:
        - configMapRef:
            name: chatroom-config
```

2. **部署应用**
```bash
kubectl apply -f k8s/
```

## 生产环境配置

### 安全配置

1. **HTTPS配置**
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    ssl_certificate /path/to/cert.pem;
    ssl_certificate_key /path/to/key.pem;
    
    # SSL配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=63072000" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
}
```

2. **防火墙配置**
```bash
# 只开放必要端口
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw enable
```

3. **数据库安全**
```sql
-- 删除默认用户
DROP USER IF EXISTS ''@'localhost';
DROP USER IF EXISTS ''@'%';

-- 设置强密码
ALTER USER 'root'@'localhost' IDENTIFIED BY 'strong_password';

-- 限制远程访问
CREATE USER 'chatroom'@'localhost' IDENTIFIED BY 'strong_password';
GRANT ALL PRIVILEGES ON chatroom.* TO 'chatroom'@'localhost';
```

### 性能优化

1. **数据库优化**
```sql
-- 配置参数
SET GLOBAL innodb_buffer_pool_size = 1073741824;  -- 1GB
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = 67108864;  -- 64MB
```

2. **Redis优化**
```conf
# redis.conf
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

3. **JVM优化**
```bash
java -jar -Xms512m -Xmx1024m \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  chatroom-backend-1.0.0.jar
```

### 监控配置

1. **应用监控**
```yaml
# docker-compose.monitoring.yml
version: '3.8'
services:
  prometheus:
    image: prom/prometheus
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml

  grafana:
    image: grafana/grafana
    ports:
      - "3000:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin
```

2. **日志收集**
```yaml
# docker-compose.logging.yml
version: '3.8'
services:
  elasticsearch:
    image: docker.elastic.co/elasticsearch/elasticsearch:7.15.0
    environment:
      - discovery.type=single-node
    ports:
      - "9200:9200"

  kibana:
    image: docker.elastic.co/kibana/kibana:7.15.0
    ports:
      - "5601:5601"
    depends_on:
      - elasticsearch
```

### 备份策略

1. **数据库备份**
```bash
#!/bin/bash
# backup.sh
DATE=$(date +%Y%m%d_%H%M%S)
mysqldump -u chatroom -p chatroom > backup_${DATE}.sql
aws s3 cp backup_${DATE}.sql s3://your-backup-bucket/
```

2. **文件备份**
```bash
#!/bin/bash
# 备份上传的文件
tar -czf uploads_backup_$(date +%Y%m%d).tar.gz uploads/
aws s3 cp uploads_backup_$(date +%Y%m%d).tar.gz s3://your-backup-bucket/
```

## 故障排除

### 常见问题

1. **服务无法启动**
```bash
# 检查端口占用
netstat -tlnp | grep :8080

# 检查日志
docker-compose logs backend
```

2. **数据库连接失败**
```bash
# 测试连接
mysql -h localhost -u chatroom -p chatroom

# 检查配置
docker-compose exec backend env | grep SPRING_DATASOURCE
```

3. **内存不足**
```bash
# 检查内存使用
free -h
docker stats

# 调整JVM参数
export JAVA_OPTS="-Xms256m -Xmx512m"
```

### 性能调优

1. **数据库慢查询**
```sql
-- 开启慢查询日志
SET GLOBAL slow_query_log = 'ON';
SET GLOBAL long_query_time = 2;

-- 查看慢查询
SHOW PROCESSLIST;
```

2. **缓存命中率**
```bash
# Redis统计信息
redis-cli info stats
```

3. **应用性能**
```bash
# JVM性能监控
jstat -gc <pid> 1s
jmap -histo <pid>
```

## 扩展部署

### 水平扩展

1. **负载均衡**
```nginx
upstream backend {
    server backend1:8080;
    server backend2:8080;
    server backend3:8080;
}

server {
    location /api/ {
        proxy_pass http://backend;
    }
}
```

2. **数据库集群**
```yaml
# MySQL主从复制
version: '3.8'
services:
  mysql-master:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
    command: --server-id=1 --log-bin=mysql-bin

  mysql-slave:
    image: mysql:8.0
    environment:
      MYSQL_ROOT_PASSWORD: password
    command: --server-id=2 --relay-log=mysql-relay
```

3. **Redis集群**
```yaml
version: '3.8'
services:
  redis-1:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes

  redis-2:
    image: redis:7-alpine
    command: redis-server --cluster-enabled yes
```
