#!/bin/bash

# 聊天室项目开发环境启动脚本

set -e

echo "🔧 启动开发环境..."

# 检查Node.js
if ! command -v node &> /dev/null; then
    echo "❌ Node.js未安装，请先安装Node.js 18+"
    exit 1
fi

# 检查Java
if ! command -v java &> /dev/null; then
    echo "❌ Java未安装，请先安装Java 17+"
    exit 1
fi

# 检查Maven
if ! command -v mvn &> /dev/null; then
    echo "❌ Maven未安装，请先安装Maven"
    exit 1
fi

# 启动数据库服务（仅MySQL和Redis）
echo "🗄️  启动数据库服务..."
docker-compose up -d mysql redis

# 等待数据库启动
echo "⏳ 等待数据库启动..."
sleep 10

# 启动后端服务
echo "🔧 启动后端服务..."
cd backend
mvn spring-boot:run &
BACKEND_PID=$!
cd ..

# 等待后端启动
echo "⏳ 等待后端服务启动..."
sleep 15

# 启动前端服务
echo "🎨 启动前端服务..."
cd frontend
npm install
npm run dev &
FRONTEND_PID=$!
cd ..

echo ""
echo "🎉 开发环境启动成功！"
echo ""
echo "📱 前端开发服务器: http://localhost:5173"
echo "🔧 后端开发服务器: http://localhost:8080"
echo "📚 API文档: http://localhost:8080/swagger-ui.html"
echo ""
echo "按 Ctrl+C 停止所有服务"

# 等待用户中断
trap 'echo "🛑 停止开发服务..."; kill $BACKEND_PID $FRONTEND_PID 2>/dev/null; docker-compose stop mysql redis; exit 0' INT

wait
