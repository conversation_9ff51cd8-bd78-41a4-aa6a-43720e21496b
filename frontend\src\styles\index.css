@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 全局样式 */
* {
  box-sizing: border-box;
}

html, body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

#root {
  height: 100%;
  min-height: 100vh;
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* 自定义动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(10px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 工具类 */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.slide-up {
  animation: slideUp 0.3s ease-out;
}

.bounce-in {
  animation: bounceIn 0.6s ease-out;
}

/* 聊天相关样式 */
.chat-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  background: linear-gradient(to bottom, #f8fafc, #f1f5f9);
}

.message-bubble {
  max-width: 70%;
  padding: 12px 16px;
  border-radius: 18px;
  margin-bottom: 8px;
  word-wrap: break-word;
  position: relative;
}

.message-bubble.own {
  background: #3b82f6;
  color: white;
  margin-left: auto;
  border-bottom-right-radius: 6px;
}

.message-bubble.other {
  background: white;
  color: #374151;
  border: 1px solid #e5e7eb;
  border-bottom-left-radius: 6px;
}

.message-time {
  font-size: 12px;
  opacity: 0.7;
  margin-top: 4px;
}

/* 输入框样式 */
.chat-input-container {
  padding: 16px;
  background: white;
  border-top: 1px solid #e5e7eb;
}

.chat-input {
  border: 1px solid #d1d5db;
  border-radius: 24px;
  padding: 12px 20px;
  font-size: 14px;
  outline: none;
  transition: all 0.2s;
}

.chat-input:focus {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 侧边栏样式 */
.sidebar {
  background: white;
  border-right: 1px solid #e5e7eb;
  height: 100vh;
  overflow-y: auto;
}

.room-item {
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-bottom: 1px solid #f3f4f6;
}

.room-item:hover {
  background-color: #f9fafb;
}

.room-item.active {
  background-color: #eff6ff;
  border-right: 3px solid #3b82f6;
}

/* 用户头像 */
.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  font-size: 16px;
}

/* 在线状态指示器 */
.online-indicator {
  width: 8px;
  height: 8px;
  background: #10b981;
  border-radius: 50%;
  position: absolute;
  bottom: 2px;
  right: 2px;
  border: 2px solid white;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .message-bubble {
    max-width: 85%;
  }
  
  .chat-messages {
    padding: 12px;
  }
  
  .chat-input-container {
    padding: 12px;
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  .chat-messages {
    background: linear-gradient(to bottom, #1f2937, #111827);
  }
  
  .message-bubble.other {
    background: #374151;
    color: #f9fafb;
    border-color: #4b5563;
  }
  
  .sidebar {
    background: #1f2937;
    border-color: #374151;
  }
  
  .room-item {
    color: #f9fafb;
    border-color: #374151;
  }
  
  .room-item:hover {
    background-color: #374151;
  }
}
