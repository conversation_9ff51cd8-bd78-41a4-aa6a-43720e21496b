import React, { useState } from 'react';
import { format, isToday, isYesterday } from 'date-fns';
import { zhCN } from 'date-fns/locale';
import { 
  EllipsisVerticalIcon,
  ArrowUturnLeftIcon,
  TrashIcon,
  DocumentIcon,
  PhotoIcon
} from '@heroicons/react/24/outline';
import { Message, User } from '@/types';
import { fileAPI } from '@/services/api';

interface MessageListProps {
  messages: Message[];
  currentUser: User;
  onRecall: (messageId: number) => void;
  onReply: (message: Message) => void;
}

interface MessageItemProps {
  message: Message;
  currentUser: User;
  onRecall: (messageId: number) => void;
  onReply: (message: Message) => void;
  showDateSeparator?: boolean;
}

const MessageItem: React.FC<MessageItemProps> = ({ 
  message, 
  currentUser, 
  onRecall, 
  onReply,
  showDateSeparator 
}) => {
  const [showActions, setShowActions] = useState(false);
  const isOwnMessage = message.senderId === currentUser.id;
  const messageTime = new Date(message.timestamp);

  // 格式化时间
  const formatMessageTime = (date: Date) => {
    if (isToday(date)) {
      return format(date, 'HH:mm', { locale: zhCN });
    } else if (isYesterday(date)) {
      return `昨天 ${format(date, 'HH:mm', { locale: zhCN })}`;
    } else {
      return format(date, 'MM-dd HH:mm', { locale: zhCN });
    }
  };

  // 格式化日期分隔符
  const formatDateSeparator = (date: Date) => {
    if (isToday(date)) {
      return '今天';
    } else if (isYesterday(date)) {
      return '昨天';
    } else {
      return format(date, 'yyyy年MM月dd日', { locale: zhCN });
    }
  };

  // 渲染消息内容
  const renderMessageContent = () => {
    if (message.isDeleted) {
      return (
        <div className="text-gray-400 italic">
          {message.content}
        </div>
      );
    }

    switch (message.type) {
      case 'TEXT':
        return (
          <div className="whitespace-pre-wrap break-words">
            {message.content}
          </div>
        );
      
      case 'IMAGE':
        return (
          <div className="space-y-2">
            {message.content && (
              <div className="text-sm">{message.content}</div>
            )}
            {message.fileUrl && (
              <div className="max-w-xs">
                <img
                  src={fileAPI.getImageUrl(message.fileUrl.split('/').pop() || '')}
                  alt={message.fileName || '图片'}
                  className="rounded-lg max-w-full h-auto cursor-pointer hover:opacity-90 transition-opacity"
                  onClick={() => {
                    // 打开图片预览
                    window.open(fileAPI.getImageUrl(message.fileUrl!.split('/').pop() || ''), '_blank');
                  }}
                />
              </div>
            )}
          </div>
        );
      
      case 'FILE':
        return (
          <div className="space-y-2">
            {message.content && (
              <div className="text-sm">{message.content}</div>
            )}
            {message.fileUrl && (
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg max-w-xs">
                <DocumentIcon className="h-8 w-8 text-gray-400" />
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {message.fileName || '文件'}
                  </p>
                  {message.fileSize && (
                    <p className="text-xs text-gray-500">
                      {(message.fileSize / 1024 / 1024).toFixed(2)} MB
                    </p>
                  )}
                </div>
                <a
                  href={fileAPI.getFileUrl(message.fileUrl.split('/').pop() || '')}
                  download={message.fileName}
                  className="text-blue-600 hover:text-blue-800 text-sm font-medium"
                >
                  下载
                </a>
              </div>
            )}
          </div>
        );
      
      case 'SYSTEM':
        return (
          <div className="text-center text-sm text-gray-500 italic">
            {message.content}
          </div>
        );
      
      default:
        return (
          <div className="whitespace-pre-wrap break-words">
            {message.content}
          </div>
        );
    }
  };

  // 系统消息特殊处理
  if (message.type === 'SYSTEM') {
    return (
      <div className="flex justify-center my-4">
        <div className="bg-gray-100 px-3 py-1 rounded-full">
          {renderMessageContent()}
        </div>
      </div>
    );
  }

  return (
    <div className="group">
      {/* 日期分隔符 */}
      {showDateSeparator && (
        <div className="flex justify-center my-4">
          <div className="bg-gray-100 px-3 py-1 rounded-full text-xs text-gray-500">
            {formatDateSeparator(messageTime)}
          </div>
        </div>
      )}

      <div className={`flex ${isOwnMessage ? 'justify-end' : 'justify-start'} mb-4`}>
        <div className={`flex max-w-xs lg:max-w-md ${isOwnMessage ? 'flex-row-reverse' : 'flex-row'}`}>
          {/* 头像 */}
          {!isOwnMessage && (
            <div className="flex-shrink-0 mr-3">
              <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center">
                {message.senderAvatar ? (
                  <img
                    src={message.senderAvatar}
                    alt={message.senderName}
                    className="w-8 h-8 rounded-full object-cover"
                  />
                ) : (
                  <span className="text-sm font-medium text-gray-600">
                    {message.senderName.charAt(0).toUpperCase()}
                  </span>
                )}
              </div>
            </div>
          )}

          {/* 消息内容 */}
          <div className={`relative ${isOwnMessage ? 'mr-3' : ''}`}>
            {/* 发送者名称 */}
            {!isOwnMessage && (
              <div className="text-xs text-gray-500 mb-1">
                {message.senderName}
              </div>
            )}

            {/* 回复消息预览 */}
            {message.replyToId && (
              <div className="mb-2 p-2 bg-gray-100 rounded border-l-2 border-gray-300 text-sm">
                <div className="text-gray-600">回复消息</div>
              </div>
            )}

            {/* 消息气泡 */}
            <div
              className={`relative px-4 py-2 rounded-lg ${
                isOwnMessage
                  ? 'bg-blue-600 text-white'
                  : 'bg-white border border-gray-200 text-gray-900'
              } ${message.isDeleted ? 'opacity-60' : ''}`}
              onMouseEnter={() => setShowActions(true)}
              onMouseLeave={() => setShowActions(false)}
            >
              {renderMessageContent()}

              {/* 消息时间 */}
              <div className={`text-xs mt-1 ${
                isOwnMessage ? 'text-blue-100' : 'text-gray-500'
              }`}>
                {formatMessageTime(messageTime)}
              </div>

              {/* 消息操作按钮 */}
              {showActions && !message.isDeleted && (
                <div className={`absolute top-0 ${
                  isOwnMessage ? 'left-0 -translate-x-full' : 'right-0 translate-x-full'
                } flex items-center space-x-1 bg-white border border-gray-200 rounded-lg shadow-lg p-1`}>
                  {/* 回复按钮 */}
                  <button
                    onClick={() => onReply(message)}
                    className="p-1 text-gray-400 hover:text-gray-600 transition-colors"
                    title="回复"
                  >
                    <ArrowUturnLeftIcon className="h-4 w-4" />
                  </button>

                  {/* 撤回按钮（仅自己的消息） */}
                  {isOwnMessage && (
                    <button
                      onClick={() => onRecall(message.id)}
                      className="p-1 text-gray-400 hover:text-red-600 transition-colors"
                      title="撤回"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const MessageList: React.FC<MessageListProps> = ({ 
  messages, 
  currentUser, 
  onRecall, 
  onReply 
}) => {
  // 按日期分组消息
  const groupMessagesByDate = (messages: Message[]) => {
    const grouped: { [key: string]: Message[] } = {};
    
    messages.forEach(message => {
      const date = format(new Date(message.timestamp), 'yyyy-MM-dd');
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(message);
    });
    
    return grouped;
  };

  const groupedMessages = groupMessagesByDate(messages);
  const dates = Object.keys(groupedMessages).sort();

  if (messages.length === 0) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <div className="text-gray-400 mb-2">
            <PhotoIcon className="h-12 w-12 mx-auto" />
          </div>
          <p className="text-gray-500">暂无消息</p>
          <p className="text-sm text-gray-400">发送第一条消息开始聊天吧！</p>
        </div>
      </div>
    );
  }

  return (
    <div className="px-6 py-4 space-y-1">
      {dates.map(date => (
        <div key={date}>
          {groupedMessages[date].map((message, index) => (
            <MessageItem
              key={message.id}
              message={message}
              currentUser={currentUser}
              onRecall={onRecall}
              onReply={onReply}
              showDateSeparator={index === 0}
            />
          ))}
        </div>
      ))}
    </div>
  );
};

export default MessageList;
