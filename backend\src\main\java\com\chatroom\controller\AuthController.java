package com.chatroom.controller;

import com.chatroom.dto.LoginRequest;
import com.chatroom.dto.RegisterRequest;
import com.chatroom.dto.AuthResponse;
import com.chatroom.entity.User;
import com.chatroom.service.UserService;
import com.chatroom.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Optional;

/**
 * 认证控制器
 */
@RestController
@RequestMapping("/auth")
@Tag(name = "认证管理", description = "用户注册、登录、登出等认证相关接口")
@CrossOrigin(origins = "*")
public class AuthController {

    private static final Logger logger = LoggerFactory.getLogger(AuthController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    @Operation(summary = "用户注册", description = "创建新用户账户")
    public ResponseEntity<?> register(@Valid @RequestBody RegisterRequest request) {
        try {
            logger.info("用户注册请求: {}", request.getUsername());

            User user = userService.registerUser(
                request.getUsername(),
                request.getEmail(),
                request.getPassword()
            );

            // 生成JWT Token
            String token = jwtUtil.generateToken(user.getUsername(), user.getId());

            AuthResponse response = new AuthResponse(
                token,
                user.getId(),
                user.getUsername(),
                user.getEmail(),
                user.getNickname(),
                user.getAvatarUrl()
            );

            return ResponseEntity.ok(response);

        } catch (Exception e) {
            logger.error("用户注册失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(new ErrorResponse(e.getMessage()));
        }
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户身份验证并获取访问令牌")
    public ResponseEntity<?> login(@Valid @RequestBody LoginRequest request) {
        try {
            logger.info("用户登录请求: {}", request.getUsernameOrEmail());

            Optional<User> userOpt = userService.authenticateUser(
                request.getUsernameOrEmail(),
                request.getPassword()
            );

            if (userOpt.isPresent()) {
                User user = userOpt.get();
                
                // 生成JWT Token
                String token = jwtUtil.generateToken(user.getUsername(), user.getId());

                AuthResponse response = new AuthResponse(
                    token,
                    user.getId(),
                    user.getUsername(),
                    user.getEmail(),
                    user.getNickname(),
                    user.getAvatarUrl()
                );

                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(new ErrorResponse("用户名或密码错误"));
            }

        } catch (Exception e) {
            logger.error("用户登录失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(new ErrorResponse("登录失败"));
        }
    }

    /**
     * 刷新Token
     */
    @PostMapping("/refresh")
    @Operation(summary = "刷新令牌", description = "使用现有令牌获取新的访问令牌")
    public ResponseEntity<?> refreshToken(@RequestHeader("Authorization") String authHeader) {
        try {
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ResponseEntity.badRequest().body(new ErrorResponse("无效的Authorization头"));
            }

            String token = authHeader.substring(7);
            
            if (!jwtUtil.isValidToken(token)) {
                return ResponseEntity.badRequest().body(new ErrorResponse("无效的Token"));
            }

            String newToken = jwtUtil.refreshToken(token);
            String username = jwtUtil.getUsernameFromToken(newToken);
            Long userId = jwtUtil.getUserIdFromToken(newToken);

            Optional<User> userOpt = userService.findByUsername(username);
            if (userOpt.isPresent()) {
                User user = userOpt.get();
                AuthResponse response = new AuthResponse(
                    newToken,
                    user.getId(),
                    user.getUsername(),
                    user.getEmail(),
                    user.getNickname(),
                    user.getAvatarUrl()
                );
                return ResponseEntity.ok(response);
            } else {
                return ResponseEntity.badRequest().body(new ErrorResponse("用户不存在"));
            }

        } catch (Exception e) {
            logger.error("Token刷新失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(new ErrorResponse("Token刷新失败"));
        }
    }

    /**
     * 验证Token
     */
    @PostMapping("/validate")
    @Operation(summary = "验证令牌", description = "验证访问令牌的有效性")
    public ResponseEntity<?> validateToken(@RequestHeader("Authorization") String authHeader) {
        try {
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                return ResponseEntity.badRequest().body(new ErrorResponse("无效的Authorization头"));
            }

            String token = authHeader.substring(7);
            
            if (jwtUtil.isValidToken(token) && !jwtUtil.isTokenExpired(token)) {
                String username = jwtUtil.getUsernameFromToken(token);
                Long userId = jwtUtil.getUserIdFromToken(token);
                
                return ResponseEntity.ok(new TokenValidationResponse(true, username, userId));
            } else {
                return ResponseEntity.ok(new TokenValidationResponse(false, null, null));
            }

        } catch (Exception e) {
            logger.error("Token验证失败: {}", e.getMessage());
            return ResponseEntity.ok(new TokenValidationResponse(false, null, null));
        }
    }

    /**
     * 错误响应类
     */
    public static class ErrorResponse {
        private String message;

        public ErrorResponse(String message) {
            this.message = message;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }
    }

    /**
     * Token验证响应类
     */
    public static class TokenValidationResponse {
        private boolean valid;
        private String username;
        private Long userId;

        public TokenValidationResponse(boolean valid, String username, Long userId) {
            this.valid = valid;
            this.username = username;
            this.userId = userId;
        }

        public boolean isValid() {
            return valid;
        }

        public void setValid(boolean valid) {
            this.valid = valid;
        }

        public String getUsername() {
            return username;
        }

        public void setUsername(String username) {
            this.username = username;
        }

        public Long getUserId() {
            return userId;
        }

        public void setUserId(Long userId) {
            this.userId = userId;
        }
    }
}
