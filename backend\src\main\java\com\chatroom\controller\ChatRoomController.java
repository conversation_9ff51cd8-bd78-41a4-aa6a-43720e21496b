package com.chatroom.controller;

import com.chatroom.dto.CreateRoomRequest;
import com.chatroom.entity.ChatRoom;
import com.chatroom.entity.User;
import com.chatroom.enums.RoomType;
import com.chatroom.service.ChatRoomService;
import com.chatroom.service.UserService;
import com.chatroom.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 聊天室控制器
 */
@RestController
@RequestMapping("/rooms")
@Tag(name = "聊天室管理", description = "聊天室创建、加入、离开等管理接口")
@CrossOrigin(origins = "*")
public class ChatRoomController {

    private static final Logger logger = LoggerFactory.getLogger(ChatRoomController.class);

    @Autowired
    private ChatRoomService chatRoomService;

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 获取公开聊天室列表
     */
    @GetMapping("/public")
    @Operation(summary = "获取公开聊天室列表", description = "获取所有公开的聊天室")
    public ResponseEntity<List<ChatRoom>> getPublicRooms() {
        try {
            List<ChatRoom> rooms = chatRoomService.getPublicRooms();
            return ResponseEntity.ok(rooms);
        } catch (Exception e) {
            logger.error("获取公开聊天室列表失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取用户参与的聊天室
     */
    @GetMapping("/my")
    @Operation(summary = "获取用户聊天室", description = "获取当前用户参与的所有聊天室")
    public ResponseEntity<List<ChatRoom>> getUserRooms(@RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            List<ChatRoom> rooms = chatRoomService.getUserRooms(userId);
            return ResponseEntity.ok(rooms);
        } catch (Exception e) {
            logger.error("获取用户聊天室失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 创建聊天室
     */
    @PostMapping
    @Operation(summary = "创建聊天室", description = "创建新的聊天室")
    public ResponseEntity<ChatRoom> createRoom(@Valid @RequestBody CreateRoomRequest request,
                                              @RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            ChatRoom room = chatRoomService.createRoom(
                request.getName(),
                request.getDescription(),
                RoomType.valueOf(request.getType().toUpperCase()),
                userId,
                request.getMaxMembers()
            );
            
            return ResponseEntity.ok(room);
        } catch (Exception e) {
            logger.error("创建聊天室失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 加入聊天室
     */
    @PostMapping("/{roomId}/join")
    @Operation(summary = "加入聊天室", description = "用户加入指定的聊天室")
    public ResponseEntity<?> joinRoom(@PathVariable Long roomId,
                                     @RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            boolean success = chatRoomService.joinRoom(roomId, userId);
            if (success) {
                return ResponseEntity.ok().build();
            } else {
                return ResponseEntity.badRequest().body("加入聊天室失败");
            }
        } catch (Exception e) {
            logger.error("加入聊天室失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 离开聊天室
     */
    @PostMapping("/{roomId}/leave")
    @Operation(summary = "离开聊天室", description = "用户离开指定的聊天室")
    public ResponseEntity<?> leaveRoom(@PathVariable Long roomId,
                                      @RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            boolean success = chatRoomService.leaveRoom(roomId, userId);
            if (success) {
                return ResponseEntity.ok().build();
            } else {
                return ResponseEntity.badRequest().body("离开聊天室失败");
            }
        } catch (Exception e) {
            logger.error("离开聊天室失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 获取聊天室详情
     */
    @GetMapping("/{roomId}")
    @Operation(summary = "获取聊天室详情", description = "获取指定聊天室的详细信息")
    public ResponseEntity<ChatRoom> getRoomDetails(@PathVariable Long roomId,
                                                  @RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            // 检查用户是否有权限访问该房间
            if (!chatRoomService.isUserMember(roomId, userId)) {
                return ResponseEntity.status(403).build();
            }
            
            Optional<ChatRoom> roomOpt = chatRoomService.getRoomById(roomId);
            if (roomOpt.isPresent()) {
                return ResponseEntity.ok(roomOpt.get());
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("获取聊天室详情失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 搜索公开聊天室
     */
    @GetMapping("/search")
    @Operation(summary = "搜索聊天室", description = "根据关键词搜索公开聊天室")
    public ResponseEntity<Page<ChatRoom>> searchRooms(@RequestParam String keyword,
                                                     @RequestParam(defaultValue = "0") int page,
                                                     @RequestParam(defaultValue = "20") int size) {
        try {
            Page<ChatRoom> rooms = chatRoomService.searchPublicRooms(keyword, page, size);
            return ResponseEntity.ok(rooms);
        } catch (Exception e) {
            logger.error("搜索聊天室失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取热门聊天室
     */
    @GetMapping("/popular")
    @Operation(summary = "获取热门聊天室", description = "获取按成员数量排序的热门聊天室")
    public ResponseEntity<List<ChatRoom>> getPopularRooms(@RequestParam(defaultValue = "10") int limit) {
        try {
            List<ChatRoom> rooms = chatRoomService.getPopularRooms(limit);
            return ResponseEntity.ok(rooms);
        } catch (Exception e) {
            logger.error("获取热门聊天室失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 创建私聊房间
     */
    @PostMapping("/direct/{targetUserId}")
    @Operation(summary = "创建私聊", description = "与指定用户创建私聊房间")
    public ResponseEntity<ChatRoom> createDirectChat(@PathVariable Long targetUserId,
                                                    @RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            if (userId.equals(targetUserId)) {
                return ResponseEntity.badRequest().build();
            }
            
            ChatRoom room = chatRoomService.createDirectChatRoom(userId, targetUserId);
            return ResponseEntity.ok(room);
        } catch (Exception e) {
            logger.error("创建私聊失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 从Token中获取用户ID
     */
    private Long getUserIdFromToken(String authHeader) {
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            throw new RuntimeException("无效的Authorization头");
        }
        
        String token = authHeader.substring(7);
        return jwtUtil.getUserIdFromToken(token);
    }
}
