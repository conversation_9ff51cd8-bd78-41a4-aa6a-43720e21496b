package com.chatroom.repository;

import com.chatroom.entity.ChatRoom;
import com.chatroom.entity.User;
import com.chatroom.enums.RoomType;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * 聊天室数据访问接口
 */
@Repository
public interface ChatRoomRepository extends JpaRepository<ChatRoom, Long> {

    /**
     * 查找激活的聊天室
     */
    List<ChatRoom> findByIsActiveTrueOrderByCreatedAtDesc();

    /**
     * 根据类型查找聊天室
     */
    List<ChatRoom> findByTypeAndIsActiveTrueOrderByCreatedAtDesc(RoomType type);

    /**
     * 查找公开聊天室
     */
    @Query("SELECT r FROM ChatRoom r WHERE r.type = 'PUBLIC' AND r.isActive = true ORDER BY r.createdAt DESC")
    List<ChatRoom> findPublicRooms();

    /**
     * 根据房主查找聊天室
     */
    List<ChatRoom> findByOwnerAndIsActiveTrueOrderByCreatedAtDesc(User owner);

    /**
     * 根据名称模糊搜索聊天室
     */
    @Query("SELECT r FROM ChatRoom r WHERE r.name LIKE %:keyword% AND r.isActive = true AND r.type = 'PUBLIC'")
    Page<ChatRoom> searchPublicRoomsByName(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 查找用户参与的聊天室
     */
    @Query("SELECT r FROM ChatRoom r JOIN r.members m WHERE m.id = :userId AND r.isActive = true ORDER BY r.updatedAt DESC")
    List<ChatRoom> findUserRooms(@Param("userId") Long userId);

    /**
     * 检查用户是否是房间成员
     */
    @Query("SELECT COUNT(r) > 0 FROM ChatRoom r JOIN r.members m WHERE r.id = :roomId AND m.id = :userId")
    boolean isUserMember(@Param("roomId") Long roomId, @Param("userId") Long userId);

    /**
     * 获取房间成员数量
     */
    @Query("SELECT SIZE(r.members) FROM ChatRoom r WHERE r.id = :roomId")
    int getRoomMemberCount(@Param("roomId") Long roomId);

    /**
     * 查找热门聊天室（按成员数量排序）
     */
    @Query("SELECT r FROM ChatRoom r WHERE r.type = 'PUBLIC' AND r.isActive = true ORDER BY SIZE(r.members) DESC")
    List<ChatRoom> findPopularRooms(Pageable pageable);

    /**
     * 查找最近创建的聊天室
     */
    List<ChatRoom> findTop10ByTypeAndIsActiveTrueOrderByCreatedAtDesc(RoomType type);

    /**
     * 统计聊天室数量
     */
    @Query("SELECT COUNT(r) FROM ChatRoom r WHERE r.isActive = true")
    long countActiveRooms();

    /**
     * 根据类型统计聊天室数量
     */
    @Query("SELECT COUNT(r) FROM ChatRoom r WHERE r.type = :type AND r.isActive = true")
    long countRoomsByType(@Param("type") RoomType type);

    /**
     * 查找两个用户之间的私聊房间
     */
    @Query("SELECT r FROM ChatRoom r JOIN r.members m1 JOIN r.members m2 " +
           "WHERE r.type = 'DIRECT' AND m1.id = :userId1 AND m2.id = :userId2 AND r.isActive = true")
    Optional<ChatRoom> findDirectChatRoom(@Param("userId1") Long userId1, @Param("userId2") Long userId2);
}
