package com.chatroom.controller;

import com.chatroom.dto.FileUploadResponse;
import com.chatroom.service.FileService;
import com.chatroom.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.io.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import java.io.IOException;

/**
 * 文件上传控制器
 */
@RestController
@RequestMapping("/files")
@Tag(name = "文件管理", description = "文件上传、下载等文件管理接口")
@CrossOrigin(origins = "*")
public class FileController {

    private static final Logger logger = LoggerFactory.getLogger(FileController.class);

    @Autowired
    private FileService fileService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 上传文件
     */
    @PostMapping("/upload")
    @Operation(summary = "上传文件", description = "上传聊天文件（图片、文档等）")
    public ResponseEntity<FileUploadResponse> uploadFile(@RequestParam("file") MultipartFile file,
                                                        @RequestHeader("Authorization") String authHeader) {
        try {
            // 验证用户身份
            Long userId = getUserIdFromToken(authHeader);
            
            logger.info("用户上传文件: userId={}, fileName={}, size={}", 
                       userId, file.getOriginalFilename(), file.getSize());

            // 上传文件
            FileUploadResponse response = fileService.uploadFile(file, userId);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("文件上传失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 上传头像
     */
    @PostMapping("/upload/avatar")
    @Operation(summary = "上传头像", description = "上传用户头像")
    public ResponseEntity<FileUploadResponse> uploadAvatar(@RequestParam("avatar") MultipartFile file,
                                                          @RequestHeader("Authorization") String authHeader) {
        try {
            // 验证用户身份
            Long userId = getUserIdFromToken(authHeader);
            
            logger.info("用户上传头像: userId={}, fileName={}, size={}", 
                       userId, file.getOriginalFilename(), file.getSize());

            // 上传头像
            FileUploadResponse response = fileService.uploadAvatar(file, userId);
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            logger.error("头像上传失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 下载文件
     */
    @GetMapping("/download/{fileName:.+}")
    @Operation(summary = "下载文件", description = "下载指定的文件")
    public ResponseEntity<Resource> downloadFile(@PathVariable String fileName,
                                                HttpServletRequest request) {
        try {
            logger.info("下载文件: fileName={}", fileName);

            // 加载文件资源
            Resource resource = fileService.loadFileAsResource(fileName);

            // 确定文件的内容类型
            String contentType = null;
            try {
                contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
            } catch (IOException ex) {
                logger.info("无法确定文件类型");
            }

            // 如果无法确定内容类型，则使用默认类型
            if (contentType == null) {
                contentType = "application/octet-stream";
            }

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CONTENT_DISPOSITION, 
                           "attachment; filename=\"" + resource.getFilename() + "\"")
                    .body(resource);
        } catch (Exception e) {
            logger.error("文件下载失败: {}", e.getMessage(), e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 获取图片（用于在线预览）
     */
    @GetMapping("/images/{fileName:.+}")
    @Operation(summary = "获取图片", description = "获取图片文件用于在线预览")
    public ResponseEntity<Resource> getImage(@PathVariable String fileName,
                                           HttpServletRequest request) {
        try {
            logger.debug("获取图片: fileName={}", fileName);

            // 加载图片资源
            Resource resource = fileService.loadFileAsResource(fileName);

            // 确定图片的内容类型
            String contentType = null;
            try {
                contentType = request.getServletContext().getMimeType(resource.getFile().getAbsolutePath());
            } catch (IOException ex) {
                logger.debug("无法确定图片类型");
            }

            // 如果无法确定内容类型，则使用默认图片类型
            if (contentType == null) {
                contentType = "image/jpeg";
            }

            return ResponseEntity.ok()
                    .contentType(MediaType.parseMediaType(contentType))
                    .header(HttpHeaders.CACHE_CONTROL, "max-age=3600") // 缓存1小时
                    .body(resource);
        } catch (Exception e) {
            logger.error("获取图片失败: {}", e.getMessage(), e);
            return ResponseEntity.notFound().build();
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/{fileName:.+}")
    @Operation(summary = "删除文件", description = "删除指定的文件")
    public ResponseEntity<?> deleteFile(@PathVariable String fileName,
                                       @RequestHeader("Authorization") String authHeader) {
        try {
            // 验证用户身份
            Long userId = getUserIdFromToken(authHeader);
            
            logger.info("用户删除文件: userId={}, fileName={}", userId, fileName);

            // 删除文件
            boolean success = fileService.deleteFile(fileName, userId);
            
            if (success) {
                return ResponseEntity.ok().build();
            } else {
                return ResponseEntity.badRequest().body("文件删除失败");
            }
        } catch (Exception e) {
            logger.error("文件删除失败: {}", e.getMessage(), e);
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 从Token中获取用户ID
     */
    private Long getUserIdFromToken(String authHeader) {
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            throw new RuntimeException("无效的Authorization头");
        }
        
        String token = authHeader.substring(7);
        return jwtUtil.getUserIdFromToken(token);
    }
}
