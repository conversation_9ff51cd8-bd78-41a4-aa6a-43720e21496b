import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { 
  PlusIcon,
  MagnifyingGlassIcon,
  UserGroupIcon,
  LockClosedIcon,
  ChatBubbleLeftRightIcon,
  FireIcon
} from '@heroicons/react/24/outline';
import { ChatRoom, User } from '@/types';
import { roomAPI } from '@/services/api';
import { toast } from 'react-hot-toast';
import CreateRoomModal from './CreateRoomModal';

interface RoomListProps {
  currentUser: User;
}

interface RoomItemProps {
  room: ChatRoom;
  onClick: () => void;
}

const RoomItem: React.FC<RoomItemProps> = ({ room, onClick }) => {
  // 获取房间类型图标
  const getRoomIcon = () => {
    switch (room.type) {
      case 'PUBLIC':
        return <UserGroupIcon className="h-5 w-5 text-blue-500" />;
      case 'PRIVATE':
        return <LockClosedIcon className="h-5 w-5 text-gray-500" />;
      case 'DIRECT':
        return <ChatBubbleLeftRightIcon className="h-5 w-5 text-green-500" />;
      default:
        return <UserGroupIcon className="h-5 w-5 text-gray-500" />;
    }
  };

  // 获取房间类型文本
  const getRoomTypeText = () => {
    switch (room.type) {
      case 'PUBLIC':
        return '公开';
      case 'PRIVATE':
        return '私有';
      case 'DIRECT':
        return '私聊';
      default:
        return '未知';
    }
  };

  return (
    <div
      onClick={onClick}
      className="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
    >
      <div className="flex items-start justify-between">
        <div className="flex items-start space-x-3 flex-1 min-w-0">
          {/* 房间图标 */}
          <div className="flex-shrink-0 mt-1">
            {getRoomIcon()}
          </div>

          {/* 房间信息 */}
          <div className="flex-1 min-w-0">
            <div className="flex items-center space-x-2">
              <h3 className="text-sm font-medium text-gray-900 truncate">
                {room.name}
              </h3>
              <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-800">
                {getRoomTypeText()}
              </span>
            </div>
            
            {room.description && (
              <p className="text-sm text-gray-500 truncate mt-1">
                {room.description}
              </p>
            )}
            
            <div className="flex items-center justify-between mt-2">
              <div className="flex items-center space-x-4 text-xs text-gray-500">
                <span>{room.memberCount} / {room.maxMembers} 成员</span>
                <span>创建于 {new Date(room.createdAt).toLocaleDateString()}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

const RoomList: React.FC<RoomListProps> = ({ currentUser }) => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<'my' | 'public' | 'popular'>('my');
  const [rooms, setRooms] = useState<ChatRoom[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [showCreateModal, setShowCreateModal] = useState(false);

  // 加载房间列表
  const loadRooms = async () => {
    setIsLoading(true);
    try {
      let roomData: ChatRoom[] = [];
      
      switch (activeTab) {
        case 'my':
          roomData = await roomAPI.getUserRooms();
          break;
        case 'public':
          roomData = await roomAPI.getPublicRooms();
          break;
        case 'popular':
          roomData = await roomAPI.getPopularRooms(20);
          break;
      }
      
      setRooms(roomData);
    } catch (error) {
      console.error('加载房间列表失败:', error);
      toast.error('加载房间列表失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 搜索房间
  const searchRooms = async () => {
    if (!searchTerm.trim()) {
      loadRooms();
      return;
    }

    setIsLoading(true);
    try {
      const result = await roomAPI.searchPublicRooms(searchTerm, 0, 50);
      setRooms(result.content);
    } catch (error) {
      console.error('搜索房间失败:', error);
      toast.error('搜索房间失败');
    } finally {
      setIsLoading(false);
    }
  };

  // 加入房间
  const handleJoinRoom = async (room: ChatRoom) => {
    try {
      // 如果不是自己的房间，先加入
      if (activeTab !== 'my') {
        await roomAPI.joinRoom(room.id);
        toast.success(`已加入房间 "${room.name}"`);
      }
      
      // 跳转到聊天室
      navigate(`/chat/room/${room.id}`);
    } catch (error) {
      console.error('加入房间失败:', error);
      toast.error('加入房间失败');
    }
  };

  // 创建房间成功回调
  const handleRoomCreated = (newRoom: ChatRoom) => {
    setShowCreateModal(false);
    toast.success(`房间 "${newRoom.name}" 创建成功`);
    
    // 如果当前在"我的房间"标签，刷新列表
    if (activeTab === 'my') {
      loadRooms();
    }
    
    // 跳转到新创建的房间
    navigate(`/chat/room/${newRoom.id}`);
  };

  // 过滤房间
  const filteredRooms = rooms.filter(room => {
    if (!searchTerm.trim()) return true;
    const searchLower = searchTerm.toLowerCase();
    return (
      room.name.toLowerCase().includes(searchLower) ||
      (room.description && room.description.toLowerCase().includes(searchLower))
    );
  });

  useEffect(() => {
    loadRooms();
  }, [activeTab]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (activeTab === 'public') {
        searchRooms();
      }
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  return (
    <div className="h-full flex flex-col">
      {/* 标题和创建按钮 */}
      <div className="flex items-center justify-between p-6 border-b border-gray-200">
        <h1 className="text-2xl font-bold text-gray-900">聊天室</h1>
        <button
          onClick={() => setShowCreateModal(true)}
          className="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
        >
          <PlusIcon className="h-4 w-4 mr-2" />
          创建房间
        </button>
      </div>

      {/* 标签页 */}
      <div className="flex border-b border-gray-200">
        <button
          onClick={() => setActiveTab('my')}
          className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
            activeTab === 'my'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
          }`}
        >
          我的房间
        </button>
        <button
          onClick={() => setActiveTab('public')}
          className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
            activeTab === 'public'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
          }`}
        >
          公开房间
        </button>
        <button
          onClick={() => setActiveTab('popular')}
          className={`flex-1 px-4 py-3 text-sm font-medium border-b-2 transition-colors ${
            activeTab === 'popular'
              ? 'border-blue-500 text-blue-600'
              : 'border-transparent text-gray-500 hover:text-gray-700'
          }`}
        >
          <FireIcon className="h-4 w-4 inline mr-1" />
          热门房间
        </button>
      </div>

      {/* 搜索框 */}
      <div className="p-4 border-b border-gray-200">
        <div className="relative">
          <MagnifyingGlassIcon className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <input
            type="text"
            placeholder="搜索房间..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg text-sm focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
      </div>

      {/* 房间列表 */}
      <div className="flex-1 overflow-y-auto p-4">
        {isLoading ? (
          <div className="flex items-center justify-center h-32">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : filteredRooms.length === 0 ? (
          <div className="flex items-center justify-center h-32">
            <div className="text-center">
              <UserGroupIcon className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {searchTerm ? '未找到匹配的房间' : '暂无房间'}
              </h3>
              <p className="text-gray-500">
                {activeTab === 'my' 
                  ? '创建或加入一个房间开始聊天吧！' 
                  : '试试搜索其他关键词'
                }
              </p>
            </div>
          </div>
        ) : (
          <div className="space-y-3">
            {filteredRooms.map(room => (
              <RoomItem
                key={room.id}
                room={room}
                onClick={() => handleJoinRoom(room)}
              />
            ))}
          </div>
        )}
      </div>

      {/* 创建房间模态框 */}
      {showCreateModal && (
        <CreateRoomModal
          onClose={() => setShowCreateModal(false)}
          onSuccess={handleRoomCreated}
        />
      )}
    </div>
  );
};

export default RoomList;
