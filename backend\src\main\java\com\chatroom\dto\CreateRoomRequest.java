package com.chatroom.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Max;

/**
 * 创建聊天室请求DTO
 */
public class CreateRoomRequest {

    @NotBlank(message = "房间名称不能为空")
    @Size(min = 1, max = 100, message = "房间名称长度必须在1-100个字符之间")
    private String name;

    @Size(max = 500, message = "房间描述长度不能超过500个字符")
    private String description;

    @NotBlank(message = "房间类型不能为空")
    private String type; // PUBLIC, PRIVATE

    @Min(value = 2, message = "房间最大成员数不能少于2人")
    @Max(value = 1000, message = "房间最大成员数不能超过1000人")
    private Integer maxMembers = 100;

    public CreateRoomRequest() {}

    public CreateRoomRequest(String name, String description, String type, Integer maxMembers) {
        this.name = name;
        this.description = description;
        this.type = type;
        this.maxMembers = maxMembers;
    }

    // Getters and Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public Integer getMaxMembers() {
        return maxMembers;
    }

    public void setMaxMembers(Integer maxMembers) {
        this.maxMembers = maxMembers;
    }

    @Override
    public String toString() {
        return "CreateRoomRequest{" +
                "name='" + name + '\'' +
                ", description='" + description + '\'' +
                ", type='" + type + '\'' +
                ", maxMembers=" + maxMembers +
                '}';
    }
}
