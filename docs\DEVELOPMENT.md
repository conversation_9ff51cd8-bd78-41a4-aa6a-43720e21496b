# 开发指南

## 环境要求

### 本地开发
- Node.js 18+
- Java 17+
- Maven 3.8+
- MySQL 8.0+
- Redis 6.0+

### Docker开发
- Docker 20.10+
- Docker Compose 2.0+

## 项目结构

```
chatroom/
├── frontend/          # React前端应用
│   ├── src/
│   │   ├── components/    # 可复用组件
│   │   ├── pages/         # 页面组件
│   │   ├── services/      # API服务
│   │   ├── store/         # 状态管理
│   │   ├── types/         # TypeScript类型
│   │   └── styles/        # 样式文件
│   ├── public/            # 静态资源
│   └── package.json       # 依赖配置
├── backend/           # Spring Boot后端服务
│   ├── src/main/java/com/chatroom/
│   │   ├── controller/    # 控制器
│   │   ├── service/       # 业务逻辑
│   │   ├── repository/    # 数据访问
│   │   ├── entity/        # 实体类
│   │   ├── dto/           # 数据传输对象
│   │   ├── config/        # 配置类
│   │   └── util/          # 工具类
│   └── pom.xml            # Maven配置
├── database/          # 数据库脚本
├── docker/            # Docker配置
├── scripts/           # 部署脚本
└── docs/              # 项目文档
```

## 快速开始

### 1. 克隆项目
```bash
git clone <repository-url>
cd chatroom
```

### 2. 使用Docker（推荐）
```bash
# 初始化项目
make setup

# 启动完整项目
make start

# 查看服务状态
make status
```

### 3. 本地开发
```bash
# 启动开发环境
make dev

# 或者手动启动
# 1. 启动数据库
make db-start

# 2. 启动后端
cd backend
mvn spring-boot:run

# 3. 启动前端
cd frontend
npm install
npm run dev
```

## 开发流程

### 前端开发

1. **安装依赖**
```bash
cd frontend
npm install
```

2. **启动开发服务器**
```bash
npm run dev
```

3. **构建生产版本**
```bash
npm run build
```

4. **运行测试**
```bash
npm test
```

### 后端开发

1. **编译项目**
```bash
cd backend
mvn compile
```

2. **运行应用**
```bash
mvn spring-boot:run
```

3. **运行测试**
```bash
mvn test
```

4. **打包应用**
```bash
mvn package
```

## 代码规范

### 前端规范
- 使用TypeScript进行类型检查
- 遵循ESLint和Prettier配置
- 组件使用函数式组件和Hooks
- 状态管理使用Zustand
- 样式使用Tailwind CSS + Ant Design

### 后端规范
- 遵循Spring Boot最佳实践
- 使用JPA进行数据访问
- 控制器只处理HTTP请求响应
- 业务逻辑放在Service层
- 使用DTO进行数据传输

## API文档

启动后端服务后，访问 http://localhost:8080/swagger-ui.html 查看API文档。

## 数据库

### 连接信息
- 主机: localhost
- 端口: 3306
- 数据库: chatroom
- 用户名: chatroom
- 密码: chatroom123

### 初始化
数据库会在首次启动时自动初始化，包括：
- 创建表结构
- 插入默认数据
- 创建索引

## 缓存

### Redis配置
- 主机: localhost
- 端口: 6379
- 数据库: 0

### 缓存策略
- 用户会话: 7天
- 用户信息: 1小时
- 房间信息: 30分钟
- 消息缓存: 1小时

## WebSocket

### 连接端点
- 开发环境: ws://localhost:8080/ws
- 生产环境: wss://your-domain.com/ws

### 消息类型
- MESSAGE: 聊天消息
- USER_JOIN: 用户加入
- USER_LEAVE: 用户离开
- USER_TYPING: 正在输入
- ROOM_UPDATE: 房间更新

## 部署

### Docker部署
```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

### 生产环境
1. 修改环境变量配置
2. 配置HTTPS证书
3. 设置反向代理
4. 配置监控和日志

## 故障排除

### 常见问题

1. **端口冲突**
   - 检查8080、3306、6379端口是否被占用
   - 修改docker-compose.yml中的端口映射

2. **数据库连接失败**
   - 确认MySQL服务已启动
   - 检查连接配置是否正确

3. **前端无法访问后端API**
   - 检查代理配置
   - 确认CORS设置

4. **WebSocket连接失败**
   - 检查防火墙设置
   - 确认WebSocket端点配置

### 日志查看
```bash
# 查看所有服务日志
make logs

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
```

## 贡献指南

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 推送到分支
5. 创建Pull Request

## 许可证

MIT License
