package com.chatroom.dto;

import jakarta.validation.constraints.Size;

/**
 * 更新用户信息请求DTO
 */
public class UpdateProfileRequest {

    @Size(max = 100, message = "昵称长度不能超过100个字符")
    private String nickname;

    @Size(max = 200, message = "个性签名长度不能超过200个字符")
    private String signature;

    private String avatarUrl;

    public UpdateProfileRequest() {}

    public UpdateProfileRequest(String nickname, String signature, String avatarUrl) {
        this.nickname = nickname;
        this.signature = signature;
        this.avatarUrl = avatarUrl;
    }

    // Getters and Setters
    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    @Override
    public String toString() {
        return "UpdateProfileRequest{" +
                "nickname='" + nickname + '\'' +
                ", signature='" + signature + '\'' +
                ", avatarUrl='" + avatarUrl + '\'' +
                '}';
    }
}
