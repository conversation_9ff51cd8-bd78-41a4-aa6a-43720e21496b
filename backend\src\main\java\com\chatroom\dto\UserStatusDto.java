package com.chatroom.dto;

/**
 * 用户状态DTO
 */
public class UserStatusDto {
    
    private Long userId;
    private String username;
    private String nickname;
    private String avatarUrl;
    private String status; // ONLINE, OFFLINE, AWAY, BUSY
    private String type;   // JOIN, LEAVE, TYPING, STOP_TYPING

    public UserStatusDto() {}

    public UserStatusDto(String username, String type) {
        this.username = username;
        this.type = type;
    }

    // Get<PERSON> and Setters
    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getNickname() {
        return nickname;
    }

    public void setNickname(String nickname) {
        this.nickname = nickname;
    }

    public String getAvatarUrl() {
        return avatarUrl;
    }

    public void setAvatarUrl(String avatarUrl) {
        this.avatarUrl = avatarUrl;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    @Override
    public String toString() {
        return "UserStatusDto{" +
                "userId=" + userId +
                ", username='" + username + '\'' +
                ", nickname='" + nickname + '\'' +
                ", status='" + status + '\'' +
                ", type='" + type + '\'' +
                '}';
    }
}
