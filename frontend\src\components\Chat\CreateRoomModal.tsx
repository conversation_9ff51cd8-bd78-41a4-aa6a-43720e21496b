import React, { useState } from 'react';
import { 
  XMarkIcon,
  UserGroupIcon,
  LockClosedIcon
} from '@heroicons/react/24/outline';
import { ChatRoom } from '@/types';
import { roomAPI } from '@/services/api';
import { toast } from 'react-hot-toast';

interface CreateRoomModalProps {
  onClose: () => void;
  onSuccess: (room: ChatRoom) => void;
}

const CreateRoomModal: React.FC<CreateRoomModalProps> = ({ onClose, onSuccess }) => {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    type: 'PUBLIC' as 'PUBLIC' | 'PRIVATE',
    maxMembers: 100
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  // 验证表单
  const validateForm = (): boolean => {
    const newErrors: { [key: string]: string } = {};

    if (!formData.name.trim()) {
      newErrors.name = '房间名称不能为空';
    } else if (formData.name.length > 100) {
      newErrors.name = '房间名称不能超过100个字符';
    }

    if (formData.description && formData.description.length > 500) {
      newErrors.description = '房间描述不能超过500个字符';
    }

    if (formData.maxMembers < 2) {
      newErrors.maxMembers = '房间最大成员数不能少于2人';
    } else if (formData.maxMembers > 1000) {
      newErrors.maxMembers = '房间最大成员数不能超过1000人';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'maxMembers' ? parseInt(value) || 0 : value
    }));

    // 清除对应字段的错误
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  // 提交表单
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const newRoom = await roomAPI.createRoom({
        name: formData.name.trim(),
        description: formData.description.trim() || undefined,
        type: formData.type,
        maxMembers: formData.maxMembers
      });

      onSuccess(newRoom);
    } catch (error: any) {
      console.error('创建房间失败:', error);
      
      // 处理服务器返回的错误
      if (error.response?.data?.message) {
        toast.error(error.response.data.message);
      } else {
        toast.error('创建房间失败，请重试');
      }
    } finally {
      setIsSubmitting(false);
    }
  };

  // 处理键盘事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Escape') {
      onClose();
    }
  };

  return (
    <div 
      className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50"
      onKeyDown={handleKeyDown}
      tabIndex={-1}
    >
      <div className="bg-white rounded-lg shadow-xl max-w-md w-full mx-4">
        {/* 标题栏 */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <h3 className="text-lg font-medium text-gray-900">创建聊天室</h3>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
            disabled={isSubmitting}
          >
            <XMarkIcon className="h-6 w-6" />
          </button>
        </div>

        {/* 表单内容 */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* 房间名称 */}
          <div>
            <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
              房间名称 <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder="输入房间名称"
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                errors.name ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={isSubmitting}
              maxLength={100}
            />
            {errors.name && (
              <p className="mt-1 text-sm text-red-600">{errors.name}</p>
            )}
          </div>

          {/* 房间描述 */}
          <div>
            <label htmlFor="description" className="block text-sm font-medium text-gray-700 mb-1">
              房间描述
            </label>
            <textarea
              id="description"
              name="description"
              value={formData.description}
              onChange={handleInputChange}
              placeholder="输入房间描述（可选）"
              rows={3}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none ${
                errors.description ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={isSubmitting}
              maxLength={500}
            />
            {errors.description && (
              <p className="mt-1 text-sm text-red-600">{errors.description}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              {formData.description.length}/500
            </p>
          </div>

          {/* 房间类型 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              房间类型 <span className="text-red-500">*</span>
            </label>
            <div className="space-y-2">
              <label className="flex items-center">
                <input
                  type="radio"
                  name="type"
                  value="PUBLIC"
                  checked={formData.type === 'PUBLIC'}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  disabled={isSubmitting}
                />
                <div className="ml-3 flex items-center">
                  <UserGroupIcon className="h-5 w-5 text-blue-500 mr-2" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">公开房间</div>
                    <div className="text-xs text-gray-500">任何人都可以搜索和加入</div>
                  </div>
                </div>
              </label>
              <label className="flex items-center">
                <input
                  type="radio"
                  name="type"
                  value="PRIVATE"
                  checked={formData.type === 'PRIVATE'}
                  onChange={handleInputChange}
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300"
                  disabled={isSubmitting}
                />
                <div className="ml-3 flex items-center">
                  <LockClosedIcon className="h-5 w-5 text-gray-500 mr-2" />
                  <div>
                    <div className="text-sm font-medium text-gray-900">私有房间</div>
                    <div className="text-xs text-gray-500">需要邀请才能加入</div>
                  </div>
                </div>
              </label>
            </div>
          </div>

          {/* 最大成员数 */}
          <div>
            <label htmlFor="maxMembers" className="block text-sm font-medium text-gray-700 mb-1">
              最大成员数 <span className="text-red-500">*</span>
            </label>
            <input
              type="number"
              id="maxMembers"
              name="maxMembers"
              value={formData.maxMembers}
              onChange={handleInputChange}
              min={2}
              max={1000}
              className={`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                errors.maxMembers ? 'border-red-300' : 'border-gray-300'
              }`}
              disabled={isSubmitting}
            />
            {errors.maxMembers && (
              <p className="mt-1 text-sm text-red-600">{errors.maxMembers}</p>
            )}
            <p className="mt-1 text-xs text-gray-500">
              建议设置为 2-1000 人
            </p>
          </div>
        </form>

        {/* 操作按钮 */}
        <div className="flex justify-end space-x-3 p-6 border-t border-gray-200">
          <button
            type="button"
            onClick={onClose}
            disabled={isSubmitting}
            className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            取消
          </button>
          <button
            type="submit"
            onClick={handleSubmit}
            disabled={isSubmitting || !formData.name.trim()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            {isSubmitting ? '创建中...' : '创建房间'}
          </button>
        </div>
      </div>
    </div>
  );
};

export default CreateRoomModal;
