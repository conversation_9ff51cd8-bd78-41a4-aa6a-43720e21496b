import React from 'react';
import ReactDOM from 'react-dom/client';
import App from './App';

// 移除初始加载动画
const removeLoadingScreen = () => {
  const loadingElement = document.querySelector('.loading-container');
  if (loadingElement) {
    loadingElement.remove();
  }
};

// 渲染应用
ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <App />
  </React.StrictMode>
);

// 应用加载完成后移除加载动画
setTimeout(removeLoadingScreen, 100);
