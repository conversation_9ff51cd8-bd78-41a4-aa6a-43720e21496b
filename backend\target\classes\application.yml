server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: chatroom-backend
  
  profiles:
    active: dev
  
  datasource:
    url: *********************************************************************************************************************
    username: chatroom
    password: chatroom123
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        use_sql_comments: true
  
  redis:
    host: localhost
    port: 6379
    password: 
    database: 0
    timeout: 5000ms
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 5000ms
  
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# JWT配置
jwt:
  secret: chatroom-jwt-secret-key-2024-very-long-and-secure
  expiration: 604800000 # 7天

# 文件上传配置
file:
  upload:
    path: ./uploads/
    max-size: 10485760 # 10MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,txt

# WebSocket配置
websocket:
  endpoint: /ws
  allowed-origins: http://localhost:5173,http://localhost:3000

# 日志配置
logging:
  level:
    com.chatroom: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# API文档配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operations-sorter: method

---
# 开发环境配置
spring:
  config:
    activate:
      on-profile: dev
  jpa:
    show-sql: true

---
# Docker环境配置
spring:
  config:
    activate:
      on-profile: docker
  datasource:
    url: *****************************************************************************************************************
  redis:
    host: redis

---
# 生产环境配置
spring:
  config:
    activate:
      on-profile: prod
  jpa:
    show-sql: false
  
logging:
  level:
    com.chatroom: INFO
    org.springframework.security: WARN
    org.hibernate.SQL: WARN
