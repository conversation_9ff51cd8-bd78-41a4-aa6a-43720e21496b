package com.chatroom.controller;

import com.chatroom.dto.SendMessageRequest;
import com.chatroom.entity.Message;
import com.chatroom.service.MessageService;
import com.chatroom.service.ChatRoomService;
import com.chatroom.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 消息控制器
 */
@RestController
@RequestMapping("/messages")
@Tag(name = "消息管理", description = "消息发送、查询、撤回等消息管理接口")
@CrossOrigin(origins = "*")
public class MessageController {

    private static final Logger logger = LoggerFactory.getLogger(MessageController.class);

    @Autowired
    private MessageService messageService;

    @Autowired
    private ChatRoomService chatRoomService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 发送消息（REST API方式）
     */
    @PostMapping
    @Operation(summary = "发送消息", description = "通过REST API发送聊天消息")
    public ResponseEntity<Message> sendMessage(@Valid @RequestBody SendMessageRequest request,
                                              @RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            // 验证用户是否有权限发送消息到该房间
            if (!chatRoomService.isUserMember(request.getRoomId(), userId)) {
                return ResponseEntity.status(403).build();
            }

            Message message = messageService.saveMessage(
                request.getRoomId(),
                userId,
                request.getContent(),
                request.getType(),
                request.getReplyToId()
            );

            return ResponseEntity.ok(message);
        } catch (Exception e) {
            logger.error("发送消息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取房间消息历史
     */
    @GetMapping("/room/{roomId}")
    @Operation(summary = "获取房间消息", description = "分页获取指定房间的消息历史")
    public ResponseEntity<Page<Message>> getRoomMessages(@PathVariable Long roomId,
                                                        @RequestParam(defaultValue = "0") int page,
                                                        @RequestParam(defaultValue = "50") int size,
                                                        @RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            // 验证用户是否有权限访问该房间
            if (!chatRoomService.isUserMember(roomId, userId)) {
                return ResponseEntity.status(403).build();
            }

            Page<Message> messages = messageService.getRoomMessages(roomId, page, size);
            return ResponseEntity.ok(messages);
        } catch (Exception e) {
            logger.error("获取房间消息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取房间最近消息
     */
    @GetMapping("/room/{roomId}/recent")
    @Operation(summary = "获取最近消息", description = "获取房间最近的消息")
    public ResponseEntity<List<Message>> getRecentMessages(@PathVariable Long roomId,
                                                          @RequestParam(defaultValue = "50") int limit,
                                                          @RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            // 验证用户是否有权限访问该房间
            if (!chatRoomService.isUserMember(roomId, userId)) {
                return ResponseEntity.status(403).build();
            }

            List<Message> messages = messageService.getRecentMessages(roomId, limit);
            return ResponseEntity.ok(messages);
        } catch (Exception e) {
            logger.error("获取最近消息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 搜索消息
     */
    @GetMapping("/search")
    @Operation(summary = "搜索消息", description = "在指定房间中搜索消息")
    public ResponseEntity<Page<Message>> searchMessages(@RequestParam Long roomId,
                                                       @RequestParam String keyword,
                                                       @RequestParam(defaultValue = "0") int page,
                                                       @RequestParam(defaultValue = "20") int size,
                                                       @RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            // 验证用户是否有权限访问该房间
            if (!chatRoomService.isUserMember(roomId, userId)) {
                return ResponseEntity.status(403).build();
            }

            Page<Message> messages = messageService.searchMessages(roomId, keyword, page, size);
            return ResponseEntity.ok(messages);
        } catch (Exception e) {
            logger.error("搜索消息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 撤回消息
     */
    @DeleteMapping("/{messageId}")
    @Operation(summary = "撤回消息", description = "撤回指定的消息")
    public ResponseEntity<?> recallMessage(@PathVariable Long messageId,
                                          @RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            boolean success = messageService.recallMessage(messageId, userId);
            if (success) {
                return ResponseEntity.ok().build();
            } else {
                return ResponseEntity.badRequest().body("消息撤回失败");
            }
        } catch (Exception e) {
            logger.error("撤回消息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 获取消息详情
     */
    @GetMapping("/{messageId}")
    @Operation(summary = "获取消息详情", description = "获取指定消息的详细信息")
    public ResponseEntity<Message> getMessageById(@PathVariable Long messageId,
                                                 @RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            Optional<Message> messageOpt = messageService.getMessageById(messageId);
            if (messageOpt.isPresent()) {
                Message message = messageOpt.get();
                
                // 验证用户是否有权限访问该消息所在的房间
                if (!chatRoomService.isUserMember(message.getRoom().getId(), userId)) {
                    return ResponseEntity.status(403).build();
                }
                
                return ResponseEntity.ok(message);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("获取消息详情失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取消息回复列表
     */
    @GetMapping("/{messageId}/replies")
    @Operation(summary = "获取消息回复", description = "获取指定消息的回复列表")
    public ResponseEntity<List<Message>> getMessageReplies(@PathVariable Long messageId,
                                                          @RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            // 先获取原消息，验证权限
            Optional<Message> messageOpt = messageService.getMessageById(messageId);
            if (!messageOpt.isPresent()) {
                return ResponseEntity.notFound().build();
            }

            Message message = messageOpt.get();
            if (!chatRoomService.isUserMember(message.getRoom().getId(), userId)) {
                return ResponseEntity.status(403).build();
            }
            
            List<Message> replies = messageService.getMessageReplies(messageId);
            return ResponseEntity.ok(replies);
        } catch (Exception e) {
            logger.error("获取消息回复失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取消息统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取消息统计", description = "获取消息相关的统计信息")
    public ResponseEntity<MessageStats> getMessageStats(@RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            long todayCount = messageService.getTodayMessageCount();
            long userMessageCount = messageService.countUserMessages(userId);
            
            MessageStats stats = new MessageStats(todayCount, userMessageCount);
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            logger.error("获取消息统计失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 从Token中获取用户ID
     */
    private Long getUserIdFromToken(String authHeader) {
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            throw new RuntimeException("无效的Authorization头");
        }
        
        String token = authHeader.substring(7);
        return jwtUtil.getUserIdFromToken(token);
    }

    /**
     * 消息统计信息类
     */
    public static class MessageStats {
        private long todayMessageCount;
        private long userMessageCount;

        public MessageStats(long todayMessageCount, long userMessageCount) {
            this.todayMessageCount = todayMessageCount;
            this.userMessageCount = userMessageCount;
        }

        public long getTodayMessageCount() {
            return todayMessageCount;
        }

        public long getUserMessageCount() {
            return userMessageCount;
        }
    }
}
