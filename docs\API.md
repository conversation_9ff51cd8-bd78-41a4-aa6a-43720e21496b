# API 文档

## 基础信息

- **Base URL**: `http://localhost:8080/api`
- **认证方式**: JWT Bearer Token
- **内容类型**: `application/json`

## 认证接口

### 用户注册
```http
POST /auth/register
```

**请求体**:
```json
{
  "username": "string",
  "email": "string",
  "password": "string"
}
```

**响应**:
```json
{
  "token": "string",
  "tokenType": "Bearer",
  "userId": 1,
  "username": "string",
  "email": "string",
  "nickname": "string",
  "avatarUrl": "string"
}
```

### 用户登录
```http
POST /auth/login
```

**请求体**:
```json
{
  "usernameOrEmail": "string",
  "password": "string"
}
```

**响应**: 同注册接口

### 刷新Token
```http
POST /auth/refresh
Authorization: Bearer <token>
```

### 验证Token
```http
POST /auth/validate
Authorization: Bearer <token>
```

## 用户接口

### 获取当前用户信息
```http
GET /users/me
Authorization: Bearer <token>
```

### 更新用户信息
```http
PUT /users/me
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "nickname": "string",
  "signature": "string",
  "avatarUrl": "string"
}
```

### 修改密码
```http
PUT /users/me/password
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "oldPassword": "string",
  "newPassword": "string"
}
```

### 搜索用户
```http
GET /users/search?keyword=string&page=0&size=20
Authorization: Bearer <token>
```

### 获取在线用户
```http
GET /users/online
Authorization: Bearer <token>
```

## 聊天室接口

### 获取聊天室列表
```http
GET /rooms
Authorization: Bearer <token>
```

### 获取用户参与的聊天室
```http
GET /rooms/my
Authorization: Bearer <token>
```

### 创建聊天室
```http
POST /rooms
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "name": "string",
  "description": "string",
  "type": "PUBLIC|PRIVATE|DIRECT",
  "maxMembers": 100
}
```

### 加入聊天室
```http
POST /rooms/{roomId}/join
Authorization: Bearer <token>
```

### 离开聊天室
```http
POST /rooms/{roomId}/leave
Authorization: Bearer <token>
```

### 获取聊天室详情
```http
GET /rooms/{roomId}
Authorization: Bearer <token>
```

### 搜索公开聊天室
```http
GET /rooms/search?keyword=string&page=0&size=20
Authorization: Bearer <token>
```

## 消息接口

### 获取聊天室消息
```http
GET /messages/room/{roomId}?page=0&size=50
Authorization: Bearer <token>
```

### 发送消息
```http
POST /messages
Authorization: Bearer <token>
```

**请求体**:
```json
{
  "roomId": 1,
  "content": "string",
  "type": "TEXT|IMAGE|FILE|SYSTEM",
  "replyToId": 1
}
```

### 撤回消息
```http
DELETE /messages/{messageId}
Authorization: Bearer <token>
```

### 搜索消息
```http
GET /messages/search?roomId=1&keyword=string&page=0&size=20
Authorization: Bearer <token>
```

## 文件接口

### 上传文件
```http
POST /files/upload
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

**请求体**:
```
file: <binary>
```

**响应**:
```json
{
  "url": "string",
  "fileName": "string"
}
```

### 上传头像
```http
POST /files/upload/avatar
Authorization: Bearer <token>
Content-Type: multipart/form-data
```

**请求体**:
```
avatar: <binary>
```

**响应**:
```json
{
  "url": "string"
}
```

## WebSocket 接口

### 连接端点
```
ws://localhost:8080/ws
```

### 消息格式

**发送消息**:
```json
{
  "type": "MESSAGE",
  "data": {
    "roomId": 1,
    "content": "Hello World",
    "type": "TEXT"
  }
}
```

**接收消息**:
```json
{
  "type": "MESSAGE",
  "data": {
    "id": 1,
    "roomId": 1,
    "sender": {
      "id": 1,
      "username": "user1",
      "nickname": "User One"
    },
    "content": "Hello World",
    "type": "TEXT",
    "createdAt": "2024-01-01T12:00:00Z"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 消息类型

- `MESSAGE`: 聊天消息
- `USER_JOIN`: 用户加入房间
- `USER_LEAVE`: 用户离开房间
- `USER_TYPING`: 用户正在输入
- `USER_STOP_TYPING`: 用户停止输入
- `ROOM_UPDATE`: 房间信息更新
- `ERROR`: 错误消息

## 错误码

| 状态码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 请求参数错误 |
| 401 | 未认证或Token无效 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 409 | 资源冲突 |
| 500 | 服务器内部错误 |

## 分页响应格式

```json
{
  "content": [],
  "totalElements": 100,
  "totalPages": 10,
  "size": 10,
  "number": 0,
  "first": true,
  "last": false
}
```

## 请求示例

### 使用curl

**登录**:
```bash
curl -X POST http://localhost:8080/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"usernameOrEmail":"user1","password":"password123"}'
```

**获取聊天室列表**:
```bash
curl -X GET http://localhost:8080/api/rooms \
  -H "Authorization: Bearer <your-token>"
```

**发送消息**:
```bash
curl -X POST http://localhost:8080/api/messages \
  -H "Authorization: Bearer <your-token>" \
  -H "Content-Type: application/json" \
  -d '{"roomId":1,"content":"Hello World","type":"TEXT"}'
```

### 使用JavaScript

```javascript
// 登录
const loginResponse = await fetch('/api/auth/login', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    usernameOrEmail: 'user1',
    password: 'password123'
  })
});

const { token } = await loginResponse.json();

// 获取聊天室列表
const roomsResponse = await fetch('/api/rooms', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});

const rooms = await roomsResponse.json();
```

## 限制说明

- 文件上传最大10MB
- 消息内容最大1000字符
- 用户名3-50字符
- 密码6-100字符
- 聊天室名称最大100字符
- API请求频率限制: 100次/分钟
