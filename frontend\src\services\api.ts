import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { toast } from 'react-hot-toast';
import { 
  LoginRequest, 
  RegisterRequest, 
  AuthResponse, 
  User, 
  ChatRoom, 
  Message, 
  ApiResponse,
  PaginatedResponse 
} from '@/types';

// 创建axios实例
const api: AxiosInstance = axios.create({
  baseURL: '/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// 请求拦截器 - 添加认证token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// 响应拦截器 - 处理错误
api.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token过期或无效，清除本地存储并跳转到登录页
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    } else if (error.response?.status >= 500) {
      toast.error('服务器错误，请稍后重试');
    } else if (error.response?.data?.message) {
      toast.error(error.response.data.message);
    } else if (error.message) {
      toast.error(error.message);
    }
    return Promise.reject(error);
  }
);

// 认证相关API
export const authAPI = {
  // 用户登录
  login: async (data: LoginRequest): Promise<AuthResponse> => {
    const response = await api.post<AuthResponse>('/auth/login', data);
    return response.data;
  },

  // 用户注册
  register: async (data: RegisterRequest): Promise<AuthResponse> => {
    const response = await api.post<AuthResponse>('/auth/register', data);
    return response.data;
  },

  // 刷新token
  refreshToken: async (): Promise<AuthResponse> => {
    const response = await api.post<AuthResponse>('/auth/refresh');
    return response.data;
  },

  // 验证token
  validateToken: async (): Promise<{ valid: boolean; username?: string; userId?: number }> => {
    const response = await api.post('/auth/validate');
    return response.data;
  },
};

// 用户相关API
export const userAPI = {
  // 获取当前用户信息
  getCurrentUser: async (): Promise<User> => {
    const response = await api.get<User>('/users/me');
    return response.data;
  },

  // 更新用户信息
  updateProfile: async (data: Partial<User>): Promise<User> => {
    const response = await api.put<User>('/users/me', data);
    return response.data;
  },

  // 修改密码
  changePassword: async (oldPassword: string, newPassword: string): Promise<void> => {
    await api.put('/users/me/password', { oldPassword, newPassword });
  },

  // 搜索用户
  searchUsers: async (keyword: string, page = 0, size = 20): Promise<PaginatedResponse<User>> => {
    const response = await api.get<PaginatedResponse<User>>('/users/search', {
      params: { keyword, page, size },
    });
    return response.data;
  },

  // 获取在线用户
  getOnlineUsers: async (): Promise<User[]> => {
    const response = await api.get<User[]>('/users/online');
    return response.data;
  },
};

// 聊天室相关API
export const roomAPI = {
  // 获取聊天室列表
  getRooms: async (): Promise<ChatRoom[]> => {
    const response = await api.get<ChatRoom[]>('/rooms');
    return response.data;
  },

  // 获取用户参与的聊天室
  getUserRooms: async (): Promise<ChatRoom[]> => {
    const response = await api.get<ChatRoom[]>('/rooms/my');
    return response.data;
  },

  // 创建聊天室
  createRoom: async (data: Partial<ChatRoom>): Promise<ChatRoom> => {
    const response = await api.post<ChatRoom>('/rooms', data);
    return response.data;
  },

  // 加入聊天室
  joinRoom: async (roomId: number): Promise<void> => {
    await api.post(`/rooms/${roomId}/join`);
  },

  // 离开聊天室
  leaveRoom: async (roomId: number): Promise<void> => {
    await api.post(`/rooms/${roomId}/leave`);
  },

  // 获取聊天室详情
  getRoomDetails: async (roomId: number): Promise<ChatRoom> => {
    const response = await api.get<ChatRoom>(`/rooms/${roomId}`);
    return response.data;
  },

  // 搜索公开聊天室
  searchPublicRooms: async (keyword: string, page = 0, size = 20): Promise<PaginatedResponse<ChatRoom>> => {
    const response = await api.get<PaginatedResponse<ChatRoom>>('/rooms/search', {
      params: { keyword, page, size },
    });
    return response.data;
  },
};

// 消息相关API
export const messageAPI = {
  // 获取聊天室消息
  getRoomMessages: async (roomId: number, page = 0, size = 50): Promise<PaginatedResponse<Message>> => {
    const response = await api.get<PaginatedResponse<Message>>(`/messages/room/${roomId}`, {
      params: { page, size },
    });
    return response.data;
  },

  // 发送消息
  sendMessage: async (roomId: number, content: string, type = 'TEXT', replyToId?: number): Promise<Message> => {
    const response = await api.post<Message>('/messages', {
      roomId,
      content,
      type,
      replyToId,
    });
    return response.data;
  },

  // 撤回消息
  recallMessage: async (messageId: number): Promise<void> => {
    await api.delete(`/messages/${messageId}`);
  },

  // 搜索消息
  searchMessages: async (roomId: number, keyword: string, page = 0, size = 20): Promise<PaginatedResponse<Message>> => {
    const response = await api.get<PaginatedResponse<Message>>('/messages/search', {
      params: { roomId, keyword, page, size },
    });
    return response.data;
  },
};

// 文件上传API
export const fileAPI = {
  // 上传文件
  uploadFile: async (file: File, onProgress?: (progress: number) => void): Promise<{ url: string; fileName: string }> => {
    const formData = new FormData();
    formData.append('file', file);

    const response = await api.post<{ url: string; fileName: string }>('/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total && onProgress) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });

    return response.data;
  },

  // 上传头像
  uploadAvatar: async (file: File, onProgress?: (progress: number) => void): Promise<{ url: string }> => {
    const formData = new FormData();
    formData.append('avatar', file);

    const response = await api.post<{ url: string }>('/files/upload/avatar', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (progressEvent.total && onProgress) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          onProgress(progress);
        }
      },
    });

    return response.data;
  },
};

export default api;
