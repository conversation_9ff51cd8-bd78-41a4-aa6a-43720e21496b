package com.chatroom.config;

import com.chatroom.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.Message;
import org.springframework.messaging.MessageChannel;
import org.springframework.messaging.simp.stomp.StompCommand;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.messaging.support.ChannelInterceptor;
import org.springframework.messaging.support.MessageHeaderAccessor;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;

import java.util.ArrayList;

/**
 * WebSocket消息通道拦截器，用于处理STOMP消息的JWT认证
 */
@Component
public class WebSocketChannelInterceptor implements ChannelInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketChannelInterceptor.class);

    @Autowired
    private JwtUtil jwtUtil;

    @Override
    public Message<?> preSend(Message<?> message, MessageChannel channel) {
        StompHeaderAccessor accessor = MessageHeaderAccessor.getAccessor(message, StompHeaderAccessor.class);
        
        if (accessor != null) {
            logger.debug("STOMP消息: command={}, destination={}", 
                        accessor.getCommand(), accessor.getDestination());

            if (StompCommand.CONNECT.equals(accessor.getCommand())) {
                // 处理CONNECT命令的认证
                handleConnect(accessor);
            } else if (StompCommand.SEND.equals(accessor.getCommand()) || 
                      StompCommand.SUBSCRIBE.equals(accessor.getCommand())) {
                // 处理SEND和SUBSCRIBE命令的认证
                handleAuthentication(accessor);
            }
        }

        return message;
    }

    /**
     * 处理CONNECT命令的认证
     */
    private void handleConnect(StompHeaderAccessor accessor) {
        try {
            // 从STOMP头中获取Authorization
            String authHeader = accessor.getFirstNativeHeader("Authorization");
            if (authHeader == null || !authHeader.startsWith("Bearer ")) {
                logger.warn("STOMP CONNECT: 缺少Authorization头或格式错误");
                return;
            }

            String token = authHeader.substring(7);
            
            // 验证JWT token
            if (!jwtUtil.validateToken(token)) {
                logger.warn("STOMP CONNECT: JWT token无效");
                return;
            }

            // 从token中提取用户信息
            String username = jwtUtil.getUsernameFromToken(token);
            Long userId = jwtUtil.getUserIdFromToken(token);

            // 将用户信息存储到会话属性中
            accessor.getSessionAttributes().put("username", username);
            accessor.getSessionAttributes().put("userId", userId);
            accessor.getSessionAttributes().put("token", token);

            // 设置Spring Security上下文
            UsernamePasswordAuthenticationToken authentication = 
                new UsernamePasswordAuthenticationToken(username, null, new ArrayList<>());
            SecurityContextHolder.getContext().setAuthentication(authentication);
            accessor.setUser(authentication);

            logger.info("STOMP CONNECT认证成功: username={}, userId={}", username, userId);

        } catch (Exception e) {
            logger.error("STOMP CONNECT认证异常: {}", e.getMessage(), e);
        }
    }

    /**
     * 处理其他命令的认证
     */
    private void handleAuthentication(StompHeaderAccessor accessor) {
        try {
            // 从会话属性中获取用户信息
            String username = (String) accessor.getSessionAttributes().get("username");
            if (username != null) {
                // 设置Spring Security上下文
                UsernamePasswordAuthenticationToken authentication = 
                    new UsernamePasswordAuthenticationToken(username, null, new ArrayList<>());
                SecurityContextHolder.getContext().setAuthentication(authentication);
                accessor.setUser(authentication);
                
                logger.debug("STOMP消息认证成功: username={}, command={}", 
                           username, accessor.getCommand());
            } else {
                logger.warn("STOMP消息认证失败: 用户未认证, command={}", accessor.getCommand());
            }

        } catch (Exception e) {
            logger.error("STOMP消息认证异常: {}", e.getMessage(), e);
        }
    }
}
