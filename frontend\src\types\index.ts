// 用户相关类型
export interface User {
  id: number;
  username: string;
  email: string;
  nickname: string;
  avatarUrl?: string;
  signature?: string;
  status: UserStatus;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  lastLoginAt?: string;
}

export enum UserStatus {
  ONLINE = 'ONLINE',
  OFFLINE = 'OFFLINE',
  AWAY = 'AWAY',
  BUSY = 'BUSY',
}

// 聊天室相关类型
export interface ChatRoom {
  id: number;
  name: string;
  description?: string;
  type: RoomType;
  owner: User;
  maxMembers: number;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  memberCount?: number;
  members?: User[];
}

export enum RoomType {
  PUBLIC = 'PUBLIC',
  PRIVATE = 'PRIVATE',
  DIRECT = 'DIRECT',
}

// 消息相关类型
export interface Message {
  id: number;
  roomId: number;
  senderId: number;
  senderName: string;
  senderAvatar?: string;
  content: string;
  type: 'TEXT' | 'IMAGE' | 'FILE' | 'SYSTEM';
  replyToId?: number;
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
  isDeleted: boolean;
  deletedAt?: string;
  timestamp: string;
  createdAt?: string;
  updatedAt?: string;
}

export enum MessageType {
  TEXT = 'TEXT',
  IMAGE = 'IMAGE',
  FILE = 'FILE',
  SYSTEM = 'SYSTEM',
}

// 认证相关类型
export interface LoginRequest {
  usernameOrEmail: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  password: string;
}

export interface AuthResponse {
  token: string;
  tokenType: string;
  userId: number;
  username: string;
  email: string;
  nickname: string;
  avatarUrl?: string;
}

// API响应类型
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface PaginatedResponse<T> {
  content: T[];
  totalElements: number;
  totalPages: number;
  size: number;
  number: number;
  first: boolean;
  last: boolean;
}

// WebSocket消息类型
export interface WebSocketMessage {
  type: WebSocketMessageType;
  data: any;
  timestamp: string;
}

export enum WebSocketMessageType {
  MESSAGE = 'MESSAGE',
  USER_JOIN = 'USER_JOIN',
  USER_LEAVE = 'USER_LEAVE',
  USER_TYPING = 'USER_TYPING',
  USER_STOP_TYPING = 'USER_STOP_TYPING',
  ROOM_UPDATE = 'ROOM_UPDATE',
  ERROR = 'ERROR',
}

// 应用状态类型
export interface AppState {
  user: User | null;
  isAuthenticated: boolean;
  currentRoom: ChatRoom | null;
  rooms: ChatRoom[];
  messages: Message[];
  onlineUsers: User[];
  isLoading: boolean;
  error: string | null;
}

// 主题类型
export interface Theme {
  mode: 'light' | 'dark';
  primaryColor: string;
  backgroundColor: string;
  textColor: string;
  borderColor: string;
}

// 文件上传类型
export interface FileUpload {
  file: File;
  progress: number;
  status: 'pending' | 'uploading' | 'success' | 'error';
  url?: string;
  error?: string;
}
