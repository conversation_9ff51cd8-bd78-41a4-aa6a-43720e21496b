{"name": "chatroom-frontend", "private": true, "version": "1.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.0", "antd": "^5.12.0", "@ant-design/icons": "^5.2.6", "zustand": "^4.4.7", "@tanstack/react-query": "^5.8.0", "axios": "^1.6.0", "socket.io-client": "^4.7.4", "framer-motion": "^10.16.0", "dayjs": "^1.11.10", "react-hot-toast": "^2.4.1", "react-dropzone": "^14.2.3", "emoji-picker-react": "^4.5.16", "react-intersection-observer": "^9.5.3"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react": "^4.1.1", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@types/node": "^20.9.0", "jest": "^29.7.0", "@testing-library/react": "^13.4.0", "@testing-library/jest-dom": "^6.1.4", "@testing-library/user-event": "^14.5.1"}}