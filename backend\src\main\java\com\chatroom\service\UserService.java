package com.chatroom.service;

import com.chatroom.entity.User;
import com.chatroom.enums.UserStatus;
import com.chatroom.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户服务类
 */
@Service
@Transactional
public class UserService {

    private static final Logger logger = LoggerFactory.getLogger(UserService.class);

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private PasswordEncoder passwordEncoder;

    /**
     * 用户注册
     */
    public User registerUser(String username, String email, String password) {
        logger.info("注册新用户: {}", username);

        // 检查用户名是否已存在
        if (userRepository.existsByUsername(username)) {
            throw new RuntimeException("用户名已存在");
        }

        // 检查邮箱是否已存在
        if (userRepository.existsByEmail(email)) {
            throw new RuntimeException("邮箱已存在");
        }

        // 创建新用户
        User user = new User();
        user.setUsername(username);
        user.setEmail(email);
        user.setPasswordHash(passwordEncoder.encode(password));
        user.setNickname(username); // 默认昵称为用户名
        user.setStatus(UserStatus.OFFLINE);
        user.setIsActive(true);

        User savedUser = userRepository.save(user);
        logger.info("用户注册成功: {}", savedUser.getUsername());
        return savedUser;
    }

    /**
     * 用户登录验证
     */
    public Optional<User> authenticateUser(String usernameOrEmail, String password) {
        logger.info("用户登录验证: {}", usernameOrEmail);

        Optional<User> userOpt = userRepository.findByUsernameOrEmail(usernameOrEmail, usernameOrEmail);
        
        if (userOpt.isPresent()) {
            User user = userOpt.get();
            if (user.getIsActive() && passwordEncoder.matches(password, user.getPasswordHash())) {
                // 更新最后登录时间
                user.setLastLoginAt(LocalDateTime.now());
                userRepository.save(user);
                logger.info("用户登录成功: {}", user.getUsername());
                return Optional.of(user);
            }
        }
        
        logger.warn("用户登录失败: {}", usernameOrEmail);
        return Optional.empty();
    }

    /**
     * 根据ID查找用户
     */
    @Transactional(readOnly = true)
    public Optional<User> findById(Long id) {
        return userRepository.findById(id);
    }

    /**
     * 根据用户名查找用户
     */
    @Transactional(readOnly = true)
    public Optional<User> findByUsername(String username) {
        return userRepository.findByUsername(username);
    }

    /**
     * 更新用户信息
     */
    public User updateUser(Long userId, String nickname, String signature, String avatarUrl) {
        logger.info("更新用户信息: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        if (nickname != null && !nickname.trim().isEmpty()) {
            user.setNickname(nickname.trim());
        }
        if (signature != null) {
            user.setSignature(signature.trim());
        }
        if (avatarUrl != null && !avatarUrl.trim().isEmpty()) {
            user.setAvatarUrl(avatarUrl.trim());
        }

        User updatedUser = userRepository.save(user);
        logger.info("用户信息更新成功: {}", updatedUser.getUsername());
        return updatedUser;
    }

    /**
     * 更新用户状态
     */
    public void updateUserStatus(Long userId, UserStatus status) {
        logger.info("更新用户状态: {} -> {}", userId, status);
        
        int updated = userRepository.updateUserStatus(userId, status);
        if (updated > 0) {
            logger.info("用户状态更新成功: {} -> {}", userId, status);
        } else {
            logger.warn("用户状态更新失败: {}", userId);
        }
    }

    /**
     * 修改密码
     */
    public void changePassword(Long userId, String oldPassword, String newPassword) {
        logger.info("用户修改密码: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        if (!passwordEncoder.matches(oldPassword, user.getPasswordHash())) {
            throw new RuntimeException("原密码不正确");
        }

        user.setPasswordHash(passwordEncoder.encode(newPassword));
        userRepository.save(user);
        logger.info("密码修改成功: {}", userId);
    }

    /**
     * 搜索用户
     */
    @Transactional(readOnly = true)
    public Page<User> searchUsers(String keyword, Pageable pageable) {
        return userRepository.searchByNickname(keyword, pageable);
    }

    /**
     * 获取在线用户列表
     */
    @Transactional(readOnly = true)
    public List<User> getOnlineUsers() {
        return userRepository.findOnlineUsers();
    }

    /**
     * 获取最近注册的用户
     */
    @Transactional(readOnly = true)
    public List<User> getRecentUsers() {
        return userRepository.findTop10ByIsActiveTrueOrderByCreatedAtDesc();
    }

    /**
     * 统计用户数据
     */
    @Transactional(readOnly = true)
    public UserStats getUserStats() {
        long totalUsers = userRepository.countActiveUsers();
        long onlineUsers = userRepository.countOnlineUsers();
        return new UserStats(totalUsers, onlineUsers);
    }

    /**
     * 禁用用户
     */
    public void deactivateUser(Long userId) {
        logger.info("禁用用户: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        user.setIsActive(false);
        user.setStatus(UserStatus.OFFLINE);
        userRepository.save(user);
        logger.info("用户禁用成功: {}", userId);
    }

    /**
     * 启用用户
     */
    public void activateUser(Long userId) {
        logger.info("启用用户: {}", userId);

        User user = userRepository.findById(userId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        user.setIsActive(true);
        userRepository.save(user);
        logger.info("用户启用成功: {}", userId);
    }

    /**
     * 用户统计数据类
     */
    public static class UserStats {
        private final long totalUsers;
        private final long onlineUsers;

        public UserStats(long totalUsers, long onlineUsers) {
            this.totalUsers = totalUsers;
            this.onlineUsers = onlineUsers;
        }

        public long getTotalUsers() {
            return totalUsers;
        }

        public long getOnlineUsers() {
            return onlineUsers;
        }
    }
}
