package com.chatroom.config;

import com.chatroom.util.JwtUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.Map;

/**
 * WebSocket握手拦截器，用于JWT认证
 */
@Component
public class WebSocketAuthInterceptor implements HandshakeInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketAuthInterceptor.class);

    @Autowired
    private JwtUtil jwtUtil;

    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response,
                                   WebSocketHandler wsHandler, Map<String, Object> attributes) throws Exception {
        
        logger.info("WebSocket握手开始: {}", request.getURI());
        
        try {
            String token = null;

            // 首先尝试从请求头中获取JWT token
            String authHeader = request.getHeaders().getFirst("Authorization");
            if (authHeader != null && authHeader.startsWith("Bearer ")) {
                token = authHeader.substring(7);
            } else {
                // 如果请求头中没有，尝试从查询参数中获取
                String query = request.getURI().getQuery();
                if (query != null) {
                    String[] params = query.split("&");
                    for (String param : params) {
                        if (param.startsWith("token=")) {
                            token = param.substring(6);
                            break;
                        }
                    }
                }
            }

            if (token == null) {
                logger.warn("WebSocket握手失败: 缺少JWT token");
                return false;
            }
            
            // 验证JWT token
            if (!jwtUtil.validateToken(token)) {
                logger.warn("WebSocket握手失败: JWT token无效");
                return false;
            }

            // 从token中提取用户信息
            String username = jwtUtil.getUsernameFromToken(token);
            Long userId = jwtUtil.getUserIdFromToken(token);

            // 将用户信息存储到WebSocket会话属性中
            attributes.put("username", username);
            attributes.put("userId", userId);
            attributes.put("token", token);

            logger.info("WebSocket握手成功: username={}, userId={}", username, userId);
            return true;

        } catch (Exception e) {
            logger.error("WebSocket握手异常: {}", e.getMessage(), e);
            return false;
        }
    }

    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response,
                               WebSocketHandler wsHandler, Exception exception) {
        if (exception != null) {
            logger.error("WebSocket握手后异常: {}", exception.getMessage(), exception);
        } else {
            logger.info("WebSocket握手完成: {}", request.getURI());
        }
    }
}
