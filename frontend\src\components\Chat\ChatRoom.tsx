import React, { useState, useEffect, useRef } from 'react';
import { useParams, useNavigate, useOutletContext } from 'react-router-dom';
import { toast } from 'react-hot-toast';
import {
  PaperAirplaneIcon,
  PaperClipIcon,
  XMarkIcon
} from '@heroicons/react/24/outline';
import { roomAPI, messageAPI } from '@/services/api';
import websocketService, { ChatMessage, UserStatus } from '@/services/websocket';
import { ChatRoom as ChatRoomType, Message, User } from '@/types';
import MessageList from './MessageList';
import UserList from './UserList';
import FileUpload from './FileUpload';

const ChatRoom: React.FC = () => {
  const { currentUser } = useOutletContext<{ currentUser: User }>();
  const { roomId } = useParams<{ roomId: string }>();
  const navigate = useNavigate();
  
  const [room, setRoom] = useState<ChatRoomType | null>(null);
  const [messages, setMessages] = useState<Message[]>([]);
  const [onlineUsers] = useState<User[]>([]);
  const [typingUsers, setTypingUsers] = useState<string[]>([]);
  const [messageInput, setMessageInput] = useState('');
  const [isLoading, setIsLoading] = useState(true);
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [replyToMessage, setReplyToMessage] = useState<Message | null>(null);
  
  const messageInputRef = useRef<HTMLTextAreaElement>(null);
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // 滚动到底部
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // 加载聊天室信息
  const loadRoomInfo = async () => {
    if (!roomId) return;
    
    try {
      const roomData = await roomAPI.getRoomDetails(Number(roomId));
      setRoom(roomData);
      
      // 加载最近消息
      const recentMessages = await messageAPI.getRecentMessages(Number(roomId), 50);
      setMessages(recentMessages.reverse()); // 反转数组，最新消息在底部
      
      setIsLoading(false);
      setTimeout(scrollToBottom, 100);
    } catch (error) {
      console.error('加载聊天室信息失败:', error);
      toast.error('加载聊天室信息失败');
      navigate('/chat');
    }
  };

  // WebSocket消息处理
  const handleNewMessage = (message: ChatMessage) => {
    const newMessage: Message = {
      id: message.id!,
      roomId: message.roomId,
      content: message.content,
      type: message.type,
      senderId: message.senderId!,
      senderName: message.senderName!,
      senderAvatar: message.senderAvatar,
      timestamp: message.timestamp!,
      replyToId: message.replyToId,
      fileUrl: message.fileUrl,
      fileName: message.fileName,
      fileSize: message.fileSize,
      isDeleted: false
    };
    
    setMessages(prev => [...prev, newMessage]);
    setTimeout(scrollToBottom, 100);
  };

  // 用户状态变化处理
  const handleUserStatusChange = (userStatus: UserStatus) => {
    if (userStatus.type === 'JOIN') {
      // 用户加入
      toast.success(`${userStatus.nickname || userStatus.username} 加入了聊天室`);
    } else if (userStatus.type === 'LEAVE') {
      // 用户离开
      toast(`${userStatus.nickname || userStatus.username} 离开了聊天室`);
    }
  };

  // 输入状态处理
  const handleTyping = (userStatus: UserStatus) => {
    if (userStatus.username === currentUser.username) return;
    
    if (userStatus.type === 'TYPING') {
      setTypingUsers(prev => {
        if (!prev.includes(userStatus.username)) {
          return [...prev, userStatus.username];
        }
        return prev;
      });
    } else if (userStatus.type === 'STOP_TYPING') {
      setTypingUsers(prev => prev.filter(user => user !== userStatus.username));
    }
  };

  // 发送消息
  const sendMessage = async () => {
    if (!messageInput.trim() || !roomId) return;
    
    const message: ChatMessage = {
      roomId: Number(roomId),
      content: messageInput.trim(),
      type: 'TEXT',
      replyToId: replyToMessage?.id
    };
    
    try {
      websocketService.sendMessage(message);
      setMessageInput('');
      setReplyToMessage(null);
      
      // 停止输入状态
      websocketService.sendStopTyping();
    } catch (error) {
      console.error('发送消息失败:', error);
      toast.error('发送消息失败');
    }
  };

  // 处理输入变化
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setMessageInput(e.target.value);
    
    // 发送正在输入状态
    websocketService.sendTyping();
    
    // 清除之前的定时器
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }
    
    // 3秒后停止输入状态
    typingTimeoutRef.current = setTimeout(() => {
      websocketService.sendStopTyping();
    }, 3000);
  };

  // 处理键盘事件
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // 文件上传成功处理
  const handleFileUpload = async (fileData: { url: string; fileName: string; fileSize: number; fileType: string }) => {
    if (!roomId) return;
    
    const message: ChatMessage = {
      roomId: Number(roomId),
      content: fileData.fileName,
      type: fileData.fileType === 'image' ? 'IMAGE' : 'FILE',
      fileUrl: fileData.url,
      fileName: fileData.fileName,
      fileSize: fileData.fileSize
    };
    
    try {
      websocketService.sendMessage(message);
      setShowFileUpload(false);
    } catch (error) {
      console.error('发送文件消息失败:', error);
      toast.error('发送文件失败');
    }
  };

  // 撤回消息
  const handleRecallMessage = async (messageId: number) => {
    try {
      await messageAPI.recallMessage(messageId);
      setMessages(prev => prev.map(msg => 
        msg.id === messageId 
          ? { ...msg, content: '[消息已撤回]', isDeleted: true }
          : msg
      ));
      toast.success('消息已撤回');
    } catch (error) {
      console.error('撤回消息失败:', error);
      toast.error('撤回消息失败');
    }
  };

  // 回复消息
  const handleReplyMessage = (message: Message) => {
    setReplyToMessage(message);
    messageInputRef.current?.focus();
  };

  // 取消回复
  const cancelReply = () => {
    setReplyToMessage(null);
  };

  useEffect(() => {
    if (!roomId) {
      navigate('/chat');
      return;
    }

    loadRoomInfo();

    // 连接WebSocket
    const token = localStorage.getItem('token');
    if (token) {
      websocketService.connect(token, {
        onMessage: handleNewMessage,
        onUserStatusChange: handleUserStatusChange,
        onTyping: handleTyping,
        onConnect: () => {
          console.log('WebSocket连接成功');
          // 加入房间
          websocketService.joinRoom(Number(roomId), {
            username: currentUser.username,
            nickname: currentUser.nickname
          });
        },
        onDisconnect: () => {
          console.log('WebSocket连接断开');
        },
        onError: (error) => {
          console.error('WebSocket错误:', error);
          toast.error('连接失败，请刷新页面重试');
        }
      });
    }

    return () => {
      // 离开房间
      websocketService.leaveRoom();
      
      // 清除定时器
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [roomId, currentUser]);

  if (isLoading) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!room) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-center">
          <h3 className="text-lg font-medium text-gray-900">聊天室不存在</h3>
          <p className="text-gray-500">请选择其他聊天室</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex h-full">
      {/* 主聊天区域 */}
      <div className="flex-1 flex flex-col">
        {/* 聊天室标题 */}
        <div className="bg-white border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-xl font-semibold text-gray-900">{room.name}</h1>
              {room.description && (
                <p className="text-sm text-gray-500">{room.description}</p>
              )}
            </div>
            <div className="flex items-center space-x-2">
              <span className="text-sm text-gray-500">
                {room.memberCount} / {room.maxMembers} 成员
              </span>
            </div>
          </div>
        </div>

        {/* 消息列表 */}
        <div className="flex-1 overflow-y-auto">
          <MessageList 
            messages={messages}
            currentUser={currentUser}
            onRecall={handleRecallMessage}
            onReply={handleReplyMessage}
          />
          
          {/* 正在输入提示 */}
          {typingUsers.length > 0 && (
            <div className="px-6 py-2 text-sm text-gray-500">
              {typingUsers.join(', ')} 正在输入...
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* 回复预览 */}
        {replyToMessage && (
          <div className="bg-gray-50 border-t border-gray-200 px-6 py-3">
            <div className="flex items-center justify-between">
              <div className="flex-1">
                <p className="text-sm text-gray-600">
                  回复 <span className="font-medium">{replyToMessage.senderName}</span>
                </p>
                <p className="text-sm text-gray-500 truncate">
                  {replyToMessage.content}
                </p>
              </div>
              <button
                onClick={cancelReply}
                className="ml-2 p-1 text-gray-400 hover:text-gray-600"
              >
                <XMarkIcon className="h-4 w-4" />
              </button>
            </div>
          </div>
        )}

        {/* 消息输入区域 */}
        <div className="bg-white border-t border-gray-200 px-6 py-4">
          <div className="flex items-end space-x-3">
            {/* 附件按钮 */}
            <button
              onClick={() => setShowFileUpload(true)}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="上传文件"
            >
              <PaperClipIcon className="h-5 w-5" />
            </button>

            {/* 输入框 */}
            <div className="flex-1">
              <textarea
                ref={messageInputRef}
                value={messageInput}
                onChange={handleInputChange}
                onKeyPress={handleKeyPress}
                placeholder="输入消息..."
                className="w-full px-3 py-2 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                rows={1}
                style={{ minHeight: '40px', maxHeight: '120px' }}
              />
            </div>

            {/* 发送按钮 */}
            <button
              onClick={sendMessage}
              disabled={!messageInput.trim()}
              className="p-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              title="发送消息"
            >
              <PaperAirplaneIcon className="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>

      {/* 用户列表侧边栏 */}
      <div className="w-64 bg-gray-50 border-l border-gray-200">
        <UserList 
          roomId={Number(roomId)}
          onlineUsers={onlineUsers}
          currentUser={currentUser}
        />
      </div>

      {/* 文件上传模态框 */}
      {showFileUpload && (
        <FileUpload
          onUpload={handleFileUpload}
          onClose={() => setShowFileUpload(false)}
        />
      )}
    </div>
  );
};

export default ChatRoom;
