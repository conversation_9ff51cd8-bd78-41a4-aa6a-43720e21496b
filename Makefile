# 聊天室项目 Makefile

.PHONY: help start stop dev clean build test logs

# 默认目标
help:
	@echo "聊天室项目管理命令："
	@echo ""
	@echo "  make start    - 启动完整项目（Docker）"
	@echo "  make stop     - 停止项目"
	@echo "  make dev      - 启动开发环境"
	@echo "  make build    - 构建项目"
	@echo "  make test     - 运行测试"
	@echo "  make clean    - 清理项目"
	@echo "  make logs     - 查看日志"
	@echo "  make setup    - 初始化项目"
	@echo ""

# 启动完整项目
start:
	@echo "🚀 启动聊天室项目..."
	@chmod +x scripts/start.sh
	@./scripts/start.sh

# 停止项目
stop:
	@echo "🛑 停止聊天室项目..."
	@chmod +x scripts/stop.sh
	@./scripts/stop.sh

# 开发环境
dev:
	@echo "🔧 启动开发环境..."
	@chmod +x scripts/dev.sh
	@./scripts/dev.sh

# 构建项目
build:
	@echo "🔨 构建项目..."
	@echo "构建后端..."
	@cd backend && mvn clean package -DskipTests
	@echo "构建前端..."
	@cd frontend && npm install && npm run build
	@echo "✅ 构建完成"

# 运行测试
test:
	@echo "🧪 运行测试..."
	@echo "后端测试..."
	@cd backend && mvn test
	@echo "前端测试..."
	@cd frontend && npm test
	@echo "✅ 测试完成"

# 清理项目
clean:
	@echo "🧹 清理项目..."
	@cd backend && mvn clean
	@cd frontend && rm -rf node_modules dist
	@docker-compose down -v
	@docker system prune -f
	@echo "✅ 清理完成"

# 查看日志
logs:
	@docker-compose logs -f

# 初始化项目
setup:
	@echo "🔧 初始化项目..."
	@if [ ! -f .env ]; then cp docker/.env.example .env; fi
	@mkdir -p uploads logs
	@chmod +x scripts/*.sh
	@echo "✅ 项目初始化完成"
	@echo "请编辑 .env 文件配置您的环境变量"

# 数据库操作
db-start:
	@echo "🗄️  启动数据库..."
	@docker-compose up -d mysql redis

db-stop:
	@echo "🗄️  停止数据库..."
	@docker-compose stop mysql redis

db-reset:
	@echo "🗄️  重置数据库..."
	@docker-compose down -v mysql redis
	@docker-compose up -d mysql redis

# 查看状态
status:
	@echo "📊 服务状态："
	@docker-compose ps
