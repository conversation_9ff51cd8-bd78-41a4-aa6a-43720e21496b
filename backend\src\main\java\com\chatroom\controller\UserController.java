package com.chatroom.controller;

import com.chatroom.dto.UpdateProfileRequest;
import com.chatroom.dto.ChangePasswordRequest;
import com.chatroom.entity.User;
import com.chatroom.enums.UserStatus;
import com.chatroom.service.UserService;
import com.chatroom.util.JwtUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Optional;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/users")
@Tag(name = "用户管理", description = "用户信息管理、状态更新等接口")
@CrossOrigin(origins = "*")
public class UserController {

    private static final Logger logger = LoggerFactory.getLogger(UserController.class);

    @Autowired
    private UserService userService;

    @Autowired
    private JwtUtil jwtUtil;

    /**
     * 获取当前用户信息
     */
    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    public ResponseEntity<User> getCurrentUser(@RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            Optional<User> userOpt = userService.findById(userId);
            if (userOpt.isPresent()) {
                return ResponseEntity.ok(userOpt.get());
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("获取当前用户信息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 更新用户信息
     */
    @PutMapping("/me")
    @Operation(summary = "更新用户信息", description = "更新当前用户的个人信息")
    public ResponseEntity<User> updateProfile(@Valid @RequestBody UpdateProfileRequest request,
                                             @RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            User updatedUser = userService.updateUser(userId, request.getNickname(), 
                                                     request.getSignature(), request.getAvatarUrl());
            
            return ResponseEntity.ok(updatedUser);
        } catch (Exception e) {
            logger.error("更新用户信息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 修改密码
     */
    @PutMapping("/me/password")
    @Operation(summary = "修改密码", description = "修改当前用户的登录密码")
    public ResponseEntity<?> changePassword(@Valid @RequestBody ChangePasswordRequest request,
                                           @RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            userService.changePassword(userId, request.getOldPassword(), request.getNewPassword());
            
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("修改密码失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 更新用户状态
     */
    @PutMapping("/me/status")
    @Operation(summary = "更新用户状态", description = "更新当前用户的在线状态")
    public ResponseEntity<?> updateStatus(@RequestParam String status,
                                         @RequestHeader("Authorization") String authHeader) {
        try {
            Long userId = getUserIdFromToken(authHeader);
            
            UserStatus userStatus = UserStatus.valueOf(status.toUpperCase());
            userService.updateUserStatus(userId, userStatus);
            
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            logger.error("更新用户状态失败: {}", e.getMessage());
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    /**
     * 搜索用户
     */
    @GetMapping("/search")
    @Operation(summary = "搜索用户", description = "根据关键词搜索用户")
    public ResponseEntity<Page<User>> searchUsers(@RequestParam String keyword,
                                                 @RequestParam(defaultValue = "0") int page,
                                                 @RequestParam(defaultValue = "20") int size) {
        try {
            Page<User> users = userService.searchUsers(keyword, 
                org.springframework.data.domain.PageRequest.of(page, size));
            return ResponseEntity.ok(users);
        } catch (Exception e) {
            logger.error("搜索用户失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取在线用户列表
     */
    @GetMapping("/online")
    @Operation(summary = "获取在线用户", description = "获取当前在线的用户列表")
    public ResponseEntity<List<User>> getOnlineUsers() {
        try {
            List<User> onlineUsers = userService.getOnlineUsers();
            return ResponseEntity.ok(onlineUsers);
        } catch (Exception e) {
            logger.error("获取在线用户失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取用户统计信息
     */
    @GetMapping("/stats")
    @Operation(summary = "获取用户统计", description = "获取用户相关的统计信息")
    public ResponseEntity<UserService.UserStats> getUserStats() {
        try {
            UserService.UserStats stats = userService.getUserStats();
            return ResponseEntity.ok(stats);
        } catch (Exception e) {
            logger.error("获取用户统计失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 获取指定用户信息
     */
    @GetMapping("/{userId}")
    @Operation(summary = "获取用户信息", description = "获取指定用户的公开信息")
    public ResponseEntity<User> getUserById(@PathVariable Long userId) {
        try {
            Optional<User> userOpt = userService.findById(userId);
            if (userOpt.isPresent()) {
                User user = userOpt.get();
                // 只返回公开信息，隐藏敏感数据
                user.setPasswordHash(null);
                return ResponseEntity.ok(user);
            } else {
                return ResponseEntity.notFound().build();
            }
        } catch (Exception e) {
            logger.error("获取用户信息失败: {}", e.getMessage());
            return ResponseEntity.badRequest().build();
        }
    }

    /**
     * 从Token中获取用户ID
     */
    private Long getUserIdFromToken(String authHeader) {
        if (authHeader == null || !authHeader.startsWith("Bearer ")) {
            throw new RuntimeException("无效的Authorization头");
        }
        
        String token = authHeader.substring(7);
        return jwtUtil.getUserIdFromToken(token);
    }
}
