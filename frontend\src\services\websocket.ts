import { io, Socket } from 'socket.io-client';

export interface ChatMessage {
  id?: number;
  roomId: number;
  content: string;
  type: 'TEXT' | 'IMAGE' | 'FILE' | 'SYSTEM';
  senderId?: number;
  senderName?: string;
  senderAvatar?: string;
  timestamp?: string;
  replyToId?: number;
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
}

export interface UserStatus {
  userId?: number;
  username: string;
  nickname?: string;
  avatarUrl?: string;
  status: 'ONLINE' | 'OFFLINE' | 'AWAY' | 'BUSY';
  type: 'JOIN' | 'LEAVE' | 'TYPING' | 'STOP_TYPING';
}

export interface WebSocketCallbacks {
  onMessage?: (message: ChatMessage) => void;
  onUserStatusChange?: (userStatus: UserStatus) => void;
  onTyping?: (userStatus: UserStatus) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: any) => void;
}

class WebSocketService {
  private socket: Socket | null = null;
  private callbacks: WebSocketCallbacks = {};
  private currentRoomId: number | null = null;
  private currentUser: { username: string; nickname?: string } | null = null;

  /**
   * 连接WebSocket
   */
  connect(token: string, callbacks: WebSocketCallbacks = {}) {
    if (this.socket?.connected) {
      console.log('WebSocket已连接');
      return;
    }

    this.callbacks = callbacks;

    // 创建WebSocket连接
    this.socket = io('ws://localhost:8080', {
      path: '/ws/socket.io/',
      transports: ['websocket'],
      auth: {
        token: token
      }
    });

    // 连接成功
    this.socket.on('connect', () => {
      console.log('WebSocket连接成功');
      this.callbacks.onConnect?.();
    });

    // 连接断开
    this.socket.on('disconnect', (reason) => {
      console.log('WebSocket连接断开:', reason);
      this.callbacks.onDisconnect?.();
    });

    // 连接错误
    this.socket.on('connect_error', (error) => {
      console.error('WebSocket连接错误:', error);
      this.callbacks.onError?.(error);
    });

    // 监听消息
    this.socket.on('message', (message: ChatMessage) => {
      console.log('收到消息:', message);
      this.callbacks.onMessage?.(message);
    });

    // 监听用户状态变化
    this.socket.on('userStatus', (userStatus: UserStatus) => {
      console.log('用户状态变化:', userStatus);
      this.callbacks.onUserStatusChange?.(userStatus);
    });

    // 监听输入状态
    this.socket.on('typing', (userStatus: UserStatus) => {
      console.log('用户输入状态:', userStatus);
      this.callbacks.onTyping?.(userStatus);
    });
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.socket) {
      this.socket.disconnect();
      this.socket = null;
      this.currentRoomId = null;
      this.currentUser = null;
    }
  }

  /**
   * 加入房间
   */
  joinRoom(roomId: number, user: { username: string; nickname?: string }) {
    if (!this.socket?.connected) {
      console.error('WebSocket未连接');
      return;
    }

    this.currentRoomId = roomId;
    this.currentUser = user;

    // 如果已经在其他房间，先离开
    if (this.currentRoomId && this.currentRoomId !== roomId) {
      this.leaveRoom();
    }

    // 订阅房间消息
    this.socket.on(`/topic/room/${roomId}`, (message: ChatMessage) => {
      this.callbacks.onMessage?.(message);
    });

    // 订阅房间用户状态
    this.socket.on(`/topic/room/${roomId}/users`, (userStatus: UserStatus) => {
      this.callbacks.onUserStatusChange?.(userStatus);
    });

    // 订阅房间输入状态
    this.socket.on(`/topic/room/${roomId}/typing`, (userStatus: UserStatus) => {
      this.callbacks.onTyping?.(userStatus);
    });

    // 发送加入房间消息
    this.socket.emit(`/app/chat.addUser/${roomId}`, {
      username: user.username,
      nickname: user.nickname,
      type: 'JOIN'
    });

    console.log(`加入房间: ${roomId}`);
  }

  /**
   * 离开房间
   */
  leaveRoom() {
    if (!this.socket?.connected || !this.currentRoomId) {
      return;
    }

    // 取消订阅房间消息
    this.socket.off(`/topic/room/${this.currentRoomId}`);
    this.socket.off(`/topic/room/${this.currentRoomId}/users`);
    this.socket.off(`/topic/room/${this.currentRoomId}/typing`);

    console.log(`离开房间: ${this.currentRoomId}`);
    this.currentRoomId = null;
  }

  /**
   * 发送消息
   */
  sendMessage(message: ChatMessage) {
    if (!this.socket?.connected || !this.currentRoomId) {
      console.error('WebSocket未连接或未加入房间');
      return;
    }

    this.socket.emit(`/app/chat.sendMessage/${this.currentRoomId}`, message);
    console.log('发送消息:', message);
  }

  /**
   * 发送正在输入状态
   */
  sendTyping() {
    if (!this.socket?.connected || !this.currentRoomId || !this.currentUser) {
      return;
    }

    this.socket.emit(`/app/chat.typing/${this.currentRoomId}`, {
      username: this.currentUser.username,
      nickname: this.currentUser.nickname,
      type: 'TYPING'
    });
  }

  /**
   * 发送停止输入状态
   */
  sendStopTyping() {
    if (!this.socket?.connected || !this.currentRoomId || !this.currentUser) {
      return;
    }

    this.socket.emit(`/app/chat.stopTyping/${this.currentRoomId}`, {
      username: this.currentUser.username,
      nickname: this.currentUser.nickname,
      type: 'STOP_TYPING'
    });
  }

  /**
   * 检查连接状态
   */
  isConnected(): boolean {
    return this.socket?.connected || false;
  }

  /**
   * 获取当前房间ID
   */
  getCurrentRoomId(): number | null {
    return this.currentRoomId;
  }
}

// 导出单例实例
export const websocketService = new WebSocketService();
export default websocketService;
