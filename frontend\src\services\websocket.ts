import { Client, IMessage } from '@stomp/stompjs';
import SockJS from 'sockjs-client';

export interface ChatMessage {
  id?: number;
  roomId: number;
  content: string;
  type: 'TEXT' | 'IMAGE' | 'FILE' | 'SYSTEM';
  senderId?: number;
  senderName?: string;
  senderAvatar?: string;
  timestamp?: string;
  replyToId?: number;
  fileUrl?: string;
  fileName?: string;
  fileSize?: number;
}

export interface UserStatus {
  userId?: number;
  username: string;
  nickname?: string;
  avatarUrl?: string;
  status: 'ONLINE' | 'OFFLINE' | 'AWAY' | 'BUSY';
  type: 'JOIN' | 'LEAVE' | 'TYPING' | 'STOP_TYPING';
}

export interface WebSocketCallbacks {
  onMessage?: (message: ChatMessage) => void;
  onUserStatusChange?: (userStatus: UserStatus) => void;
  onTyping?: (userStatus: UserStatus) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: any) => void;
}

class WebSocketService {
  private client: Client | null = null;
  private callbacks: WebSocketCallbacks = {};
  private currentRoomId: number | null = null;
  private currentUser: { username: string; nickname?: string } | null = null;
  private isConnected: boolean = false;

  /**
   * 连接WebSocket
   */
  connect(token: string, callbacks: WebSocketCallbacks = {}) {
    if (this.isConnected) {
      console.log('WebSocket已连接');
      return;
    }

    this.callbacks = callbacks;

    // 创建STOMP客户端
    this.client = new Client({
      webSocketFactory: () => new SockJS(`http://localhost:8080/api/ws?token=${token}`),
      connectHeaders: {
        Authorization: `Bearer ${token}`
      },
      debug: (str) => {
        console.log('STOMP Debug:', str);
      },
      reconnectDelay: 5000,
      heartbeatIncoming: 4000,
      heartbeatOutgoing: 4000,
    });

    // 连接成功
    this.client.onConnect = (frame) => {
      console.log('WebSocket连接成功:', frame);
      this.isConnected = true;
      this.callbacks.onConnect?.();
    };

    // 连接断开
    this.client.onDisconnect = (frame) => {
      console.log('WebSocket连接断开:', frame);
      this.isConnected = false;
      this.callbacks.onDisconnect?.();
    };

    // 连接错误
    this.client.onStompError = (frame) => {
      console.error('WebSocket连接错误:', frame);
      this.isConnected = false;
      this.callbacks.onError?.(frame);
    };

    // 激活连接
    this.client.activate();
  }

  /**
   * 断开连接
   */
  disconnect() {
    if (this.client) {
      this.client.deactivate();
      this.client = null;
      this.isConnected = false;
      this.currentRoomId = null;
      this.currentUser = null;
    }
  }

  /**
   * 加入房间
   */
  joinRoom(roomId: number, user: { username: string; nickname?: string }) {
    if (!this.isConnected || !this.client) {
      console.error('WebSocket未连接');
      return;
    }

    // 如果已经在其他房间，先离开
    if (this.currentRoomId && this.currentRoomId !== roomId) {
      this.leaveRoom();
    }

    this.currentRoomId = roomId;
    this.currentUser = user;

    // 订阅房间消息
    this.client.subscribe(`/topic/room/${roomId}`, (message: IMessage) => {
      try {
        const chatMessage: ChatMessage = JSON.parse(message.body);
        console.log('收到消息:', chatMessage);
        this.callbacks.onMessage?.(chatMessage);
      } catch (error) {
        console.error('解析消息失败:', error);
      }
    });

    // 订阅房间用户状态
    this.client.subscribe(`/topic/room/${roomId}/users`, (message: IMessage) => {
      try {
        const userStatus: UserStatus = JSON.parse(message.body);
        console.log('用户状态变化:', userStatus);
        this.callbacks.onUserStatusChange?.(userStatus);
      } catch (error) {
        console.error('解析用户状态失败:', error);
      }
    });

    // 订阅房间输入状态
    this.client.subscribe(`/topic/room/${roomId}/typing`, (message: IMessage) => {
      try {
        const userStatus: UserStatus = JSON.parse(message.body);
        console.log('用户输入状态:', userStatus);
        this.callbacks.onTyping?.(userStatus);
      } catch (error) {
        console.error('解析输入状态失败:', error);
      }
    });

    // 发送加入房间消息
    this.client.publish({
      destination: `/app/chat.addUser/${roomId}`,
      body: JSON.stringify({
        username: user.username,
        nickname: user.nickname,
        type: 'JOIN'
      })
    });

    console.log(`加入房间: ${roomId}`);
  }

  /**
   * 离开房间
   */
  leaveRoom() {
    if (!this.isConnected || !this.client || !this.currentRoomId || !this.currentUser) {
      return;
    }

    // 发送离开房间消息
    this.client.publish({
      destination: `/app/chat.removeUser/${this.currentRoomId}`,
      body: JSON.stringify({
        username: this.currentUser.username,
        nickname: this.currentUser.nickname,
        type: 'LEAVE'
      })
    });

    console.log(`离开房间: ${this.currentRoomId}`);
    this.currentRoomId = null;
    this.currentUser = null;
  }

  /**
   * 发送消息
   */
  sendMessage(message: ChatMessage) {
    if (!this.isConnected || !this.client || !this.currentRoomId) {
      console.error('WebSocket未连接或未加入房间');
      return;
    }

    this.client.publish({
      destination: `/app/chat.sendMessage/${this.currentRoomId}`,
      body: JSON.stringify(message)
    });
    console.log('发送消息:', message);
  }

  /**
   * 发送正在输入状态
   */
  sendTyping() {
    if (!this.isConnected || !this.client || !this.currentRoomId || !this.currentUser) {
      return;
    }

    this.client.publish({
      destination: `/app/chat.typing/${this.currentRoomId}`,
      body: JSON.stringify({
        username: this.currentUser.username,
        nickname: this.currentUser.nickname,
        type: 'TYPING'
      })
    });
  }

  /**
   * 发送停止输入状态
   */
  sendStopTyping() {
    if (!this.isConnected || !this.client || !this.currentRoomId || !this.currentUser) {
      return;
    }

    this.client.publish({
      destination: `/app/chat.stopTyping/${this.currentRoomId}`,
      body: JSON.stringify({
        username: this.currentUser.username,
        nickname: this.currentUser.nickname,
        type: 'STOP_TYPING'
      })
    });
  }

  /**
   * 检查连接状态
   */
  getConnectionStatus(): boolean {
    return this.isConnected;
  }

  /**
   * 获取当前房间ID
   */
  getCurrentRoomId(): number | null {
    return this.currentRoomId;
  }
}

// 导出单例实例
export const websocketService = new WebSocketService();
export default websocketService;
