# 环境变量
.env
.env.local
.env.production

# 日志文件
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 依赖目录
node_modules/
.npm
.yarn

# 构建输出
dist/
build/
target/

# IDE文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Java相关
*.class
*.jar
*.war
*.ear
*.zip
*.tar.gz
*.rar
hs_err_pid*

# Maven
.mvn/
mvnw
mvnw.cmd

# Spring Boot
spring-boot-*.jar

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 上传文件
uploads/
temp/

# 缓存文件
.cache/
.parcel-cache/

# 测试覆盖率
coverage/
.nyc_output/

# 临时文件
tmp/
temp/
*.tmp
*.temp

# Docker
.dockerignore

# 备份文件
*.bak
*.backup
*.old

# 编辑器配置
.editorconfig

# 本地配置
local.properties
application-local.yml
application-local.properties
