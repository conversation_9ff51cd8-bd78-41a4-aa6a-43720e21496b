package com.chatroom.service;

import com.chatroom.dto.FileUploadResponse;
import com.chatroom.entity.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.Resource;
import org.springframework.core.io.UrlResource;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.net.MalformedURLException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

/**
 * 文件服务类
 */
@Service
public class FileService {

    private static final Logger logger = LoggerFactory.getLogger(FileService.class);

    @Value("${file.upload.path:./uploads/}")
    private String uploadPath;

    @Value("${file.upload.max-size:10485760}")
    private long maxFileSize;

    @Value("${file.upload.allowed-types:jpg,jpeg,png,gif,pdf,doc,docx,txt}")
    private String allowedTypes;

    @Autowired
    private UserService userService;

    private Path fileStorageLocation;

    /**
     * 初始化文件存储位置
     */
    public void init() {
        try {
            this.fileStorageLocation = Paths.get(uploadPath).toAbsolutePath().normalize();
            Files.createDirectories(this.fileStorageLocation);
            logger.info("文件存储目录初始化成功: {}", this.fileStorageLocation);
        } catch (Exception ex) {
            logger.error("无法创建文件存储目录: {}", ex.getMessage());
            throw new RuntimeException("无法创建文件存储目录", ex);
        }
    }

    /**
     * 上传文件
     */
    public FileUploadResponse uploadFile(MultipartFile file, Long userId) {
        // 初始化存储位置（如果还没有初始化）
        if (fileStorageLocation == null) {
            init();
        }

        // 验证文件
        validateFile(file);

        try {
            // 生成唯一文件名
            String originalFileName = StringUtils.cleanPath(file.getOriginalFilename());
            String fileExtension = getFileExtension(originalFileName);
            String fileName = generateFileName(originalFileName, fileExtension);

            // 创建日期目录
            String dateDir = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            Path dateDirPath = fileStorageLocation.resolve(dateDir);
            Files.createDirectories(dateDirPath);

            // 保存文件
            Path targetLocation = dateDirPath.resolve(fileName);
            Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);

            // 构建文件URL
            String fileUrl = "/api/files/download/" + dateDir + "/" + fileName;
            String relativePath = dateDir + "/" + fileName;

            logger.info("文件上传成功: userId={}, fileName={}, path={}", userId, originalFileName, relativePath);

            return new FileUploadResponse(fileUrl, originalFileName, file.getSize(), relativePath);

        } catch (IOException ex) {
            logger.error("文件上传失败: {}", ex.getMessage(), ex);
            throw new RuntimeException("文件上传失败: " + ex.getMessage(), ex);
        }
    }

    /**
     * 上传头像
     */
    public FileUploadResponse uploadAvatar(MultipartFile file, Long userId) {
        // 验证是否是图片文件
        if (!isImageFile(file)) {
            throw new RuntimeException("头像必须是图片文件");
        }

        // 上传文件
        FileUploadResponse response = uploadFile(file, userId);

        try {
            // 为头像创建图片预览URL
            String avatarUrl = response.getUrl().replace("/download/", "/images/");

            // 更新用户头像URL
            User user = userService.findById(userId)
                    .orElseThrow(() -> new RuntimeException("用户不存在"));

            userService.updateUser(userId, user.getNickname(), user.getSignature(), avatarUrl);

            // 更新响应中的URL
            response.setUrl(avatarUrl);

            logger.info("用户头像更新成功: userId={}, avatarUrl={}", userId, avatarUrl);

        } catch (Exception e) {
            logger.error("更新用户头像失败: {}", e.getMessage(), e);
            // 即使更新用户信息失败，文件已经上传成功，仍然返回文件信息
        }

        return response;
    }

    /**
     * 加载文件资源
     */
    public Resource loadFileAsResource(String fileName) {
        try {
            if (fileStorageLocation == null) {
                init();
            }

            Path filePath = fileStorageLocation.resolve(fileName).normalize();
            Resource resource = new UrlResource(filePath.toUri());

            if (resource.exists()) {
                return resource;
            } else {
                throw new RuntimeException("文件不存在: " + fileName);
            }
        } catch (MalformedURLException ex) {
            logger.error("文件路径错误: {}", ex.getMessage());
            throw new RuntimeException("文件路径错误: " + fileName, ex);
        }
    }

    /**
     * 删除文件
     */
    public boolean deleteFile(String fileName, Long userId) {
        try {
            if (fileStorageLocation == null) {
                init();
            }

            Path filePath = fileStorageLocation.resolve(fileName).normalize();
            
            if (Files.exists(filePath)) {
                Files.delete(filePath);
                logger.info("文件删除成功: userId={}, fileName={}", userId, fileName);
                return true;
            } else {
                logger.warn("要删除的文件不存在: fileName={}", fileName);
                return false;
            }
        } catch (IOException ex) {
            logger.error("文件删除失败: {}", ex.getMessage(), ex);
            return false;
        }
    }

    /**
     * 验证文件
     */
    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new RuntimeException("文件不能为空");
        }

        if (file.getSize() > maxFileSize) {
            throw new RuntimeException("文件大小超过限制: " + (maxFileSize / 1024 / 1024) + "MB");
        }

        String fileName = file.getOriginalFilename();
        if (fileName == null || fileName.contains("..")) {
            throw new RuntimeException("文件名无效");
        }

        String fileExtension = getFileExtension(fileName).toLowerCase();
        List<String> allowedExtensions = Arrays.asList(allowedTypes.split(","));
        
        if (!allowedExtensions.contains(fileExtension)) {
            throw new RuntimeException("不支持的文件类型: " + fileExtension);
        }
    }

    /**
     * 检查是否是图片文件
     */
    private boolean isImageFile(MultipartFile file) {
        String fileName = file.getOriginalFilename();
        if (fileName == null) {
            return false;
        }

        String fileExtension = getFileExtension(fileName).toLowerCase();
        List<String> imageExtensions = Arrays.asList("jpg", "jpeg", "png", "gif", "bmp", "webp");
        
        return imageExtensions.contains(fileExtension);
    }

    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }

    /**
     * 生成唯一文件名
     */
    private String generateFileName(String originalFileName, String extension) {
        String uuid = UUID.randomUUID().toString().replace("-", "");
        String timestamp = String.valueOf(System.currentTimeMillis());
        return uuid + "_" + timestamp + "." + extension;
    }
}
