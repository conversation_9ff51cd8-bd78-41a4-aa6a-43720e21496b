import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { User, LoginRequest, RegisterRequest, AuthResponse } from '@/types';
import { authAPI, userAPI } from '@/services/api';
import { toast } from 'react-hot-toast';

interface AuthState {
  // 状态
  user: User | null;
  token: string | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;

  // 操作
  login: (data: LoginRequest) => Promise<void>;
  register: (data: RegisterRequest) => Promise<void>;
  logout: () => void;
  refreshToken: () => Promise<void>;
  updateProfile: (data: Partial<User>) => Promise<void>;
  clearError: () => void;
  setLoading: (loading: boolean) => void;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      // 初始状态
      user: null,
      token: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,

      // 登录
      login: async (data: LoginRequest) => {
        try {
          set({ isLoading: true, error: null });
          
          const response: AuthResponse = await authAPI.login(data);
          
          // 保存token到localStorage
          localStorage.setItem('token', response.token);
          
          // 构造用户对象
          const user: User = {
            id: response.userId,
            username: response.username,
            email: response.email,
            nickname: response.nickname,
            avatarUrl: response.avatarUrl,
            signature: '',
            status: 'ONLINE' as any,
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          set({
            user,
            token: response.token,
            isAuthenticated: true,
            isLoading: false,
          });

          toast.success('登录成功');
        } catch (error: any) {
          set({
            error: error.response?.data?.message || '登录失败',
            isLoading: false,
          });
          throw error;
        }
      },

      // 注册
      register: async (data: RegisterRequest) => {
        try {
          set({ isLoading: true, error: null });
          
          const response: AuthResponse = await authAPI.register(data);
          
          // 保存token到localStorage
          localStorage.setItem('token', response.token);
          
          // 构造用户对象
          const user: User = {
            id: response.userId,
            username: response.username,
            email: response.email,
            nickname: response.nickname,
            avatarUrl: response.avatarUrl,
            signature: '',
            status: 'ONLINE' as any,
            isActive: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
          };

          set({
            user,
            token: response.token,
            isAuthenticated: true,
            isLoading: false,
          });

          toast.success('注册成功');
        } catch (error: any) {
          set({
            error: error.response?.data?.message || '注册失败',
            isLoading: false,
          });
          throw error;
        }
      },

      // 登出
      logout: () => {
        localStorage.removeItem('token');
        localStorage.removeItem('user');
        set({
          user: null,
          token: null,
          isAuthenticated: false,
          error: null,
        });
        toast.success('已退出登录');
      },

      // 刷新token
      refreshToken: async () => {
        try {
          const response: AuthResponse = await authAPI.refreshToken();
          localStorage.setItem('token', response.token);
          
          set({
            token: response.token,
          });
        } catch (error) {
          // 刷新失败，清除认证状态
          get().logout();
          throw error;
        }
      },

      // 更新用户信息
      updateProfile: async (data: Partial<User>) => {
        try {
          set({ isLoading: true, error: null });
          
          const updatedUser = await userAPI.updateProfile(data);
          
          set({
            user: updatedUser,
            isLoading: false,
          });

          toast.success('个人信息更新成功');
        } catch (error: any) {
          set({
            error: error.response?.data?.message || '更新失败',
            isLoading: false,
          });
          throw error;
        }
      },

      // 清除错误
      clearError: () => {
        set({ error: null });
      },

      // 设置加载状态
      setLoading: (loading: boolean) => {
        set({ isLoading: loading });
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);
