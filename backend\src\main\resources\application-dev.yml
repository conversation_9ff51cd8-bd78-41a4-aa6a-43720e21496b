server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: chatroom-backend
  
  # H2数据库配置（用于开发测试）
  datasource:
    url: jdbc:h2:mem:chatroom;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    driver-class-name: org.h2.Driver
    
  h2:
    console:
      enabled: true
      path: /h2-console
  
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: true
    properties:
      hibernate:
        dialect: org.hibernate.dialect.H2Dialect
        format_sql: true
        use_sql_comments: true
  
  # Redis配置（可选，如果没有Redis则禁用）
  data:
    redis:
      repositories:
        enabled: false
  
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

  jackson:
    serialization:
      fail-on-empty-beans: false
    properties:
      hibernate:
        enable_lazy_load_no_trans: true

# JWT配置
jwt:
  secret: chatroom-jwt-secret-key-2024-very-long-and-secure-for-development
  expiration: 604800000 # 7天

# 文件上传配置
file:
  upload:
    path: ./uploads/
    max-size: 10485760 # 10MB
    allowed-types: jpg,jpeg,png,gif,pdf,doc,docx,txt

# WebSocket配置
websocket:
  endpoint: /ws
  allowed-origins: http://localhost:5173,http://localhost:3000

# 日志配置
logging:
  level:
    com.chatroom: DEBUG
    org.springframework.security: DEBUG
    org.hibernate.SQL: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"

# API文档配置
springdoc:
  api-docs:
    path: /api-docs
  swagger-ui:
    path: /swagger-ui.html
    operations-sorter: method
