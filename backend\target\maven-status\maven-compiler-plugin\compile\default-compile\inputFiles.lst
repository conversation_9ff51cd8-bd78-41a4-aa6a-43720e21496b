C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\enums\MemberRole.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\repository\MessageRepository.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\service\UserService.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\entity\Message.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\dto\RegisterRequest.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\entity\ChatRoom.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\enums\RoomType.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\repository\UserRepository.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\dto\LoginRequest.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\entity\User.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\enums\UserStatus.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\config\WebSocketConfig.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\ChatroomApplication.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\enums\MessageType.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\util\JwtUtil.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\controller\AuthController.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\repository\ChatRoomRepository.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\config\SecurityConfig.java
C:\Users\<USER>\Desktop\test\backend\src\main\java\com\chatroom\dto\AuthResponse.java
