package com.chatroom.dto;

/**
 * 文件上传响应DTO
 */
public class FileUploadResponse {
    
    private String url;
    private String fileName;
    private Long fileSize;
    private String filePath;
    private String fileType;

    public FileUploadResponse() {}

    public FileUploadResponse(String url, String fileName, Long fileSize, String filePath) {
        this.url = url;
        this.fileName = fileName;
        this.fileSize = fileSize;
        this.filePath = filePath;
        this.fileType = getFileTypeFromName(fileName);
    }

    // Getters and Setters
    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
        this.fileType = getFileTypeFromName(fileName);
    }

    public Long getFileSize() {
        return fileSize;
    }

    public void setFileSize(Long fileSize) {
        this.fileSize = fileSize;
    }

    public String getFilePath() {
        return filePath;
    }

    public void setFilePath(String filePath) {
        this.filePath = filePath;
    }

    public String getFileType() {
        return fileType;
    }

    public void setFileType(String fileType) {
        this.fileType = fileType;
    }

    /**
     * 根据文件名获取文件类型
     */
    private String getFileTypeFromName(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "unknown";
        }
        
        String extension = fileName.substring(fileName.lastIndexOf(".") + 1).toLowerCase();
        
        switch (extension) {
            case "jpg":
            case "jpeg":
            case "png":
            case "gif":
            case "bmp":
            case "webp":
                return "image";
            case "pdf":
                return "pdf";
            case "doc":
            case "docx":
                return "document";
            case "txt":
                return "text";
            case "mp4":
            case "avi":
            case "mov":
                return "video";
            case "mp3":
            case "wav":
                return "audio";
            default:
                return "file";
        }
    }

    @Override
    public String toString() {
        return "FileUploadResponse{" +
                "url='" + url + '\'' +
                ", fileName='" + fileName + '\'' +
                ", fileSize=" + fileSize +
                ", filePath='" + filePath + '\'' +
                ", fileType='" + fileType + '\'' +
                '}';
    }
}
