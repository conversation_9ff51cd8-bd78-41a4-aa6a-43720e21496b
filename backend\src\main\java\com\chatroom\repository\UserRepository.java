package com.chatroom.repository;

import com.chatroom.entity.User;
import com.chatroom.enums.UserStatus;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 用户数据访问接口
 */
@Repository
public interface UserRepository extends JpaRepository<User, Long> {

    /**
     * 根据用户名查找用户
     */
    Optional<User> findByUsername(String username);

    /**
     * 根据邮箱查找用户
     */
    Optional<User> findByEmail(String email);

    /**
     * 根据用户名或邮箱查找用户
     */
    Optional<User> findByUsernameOrEmail(String username, String email);

    /**
     * 检查用户名是否存在
     */
    boolean existsByUsername(String username);

    /**
     * 检查邮箱是否存在
     */
    boolean existsByEmail(String email);

    /**
     * 查找激活的用户
     */
    List<User> findByIsActiveTrue();

    /**
     * 根据状态查找用户
     */
    List<User> findByStatus(UserStatus status);

    /**
     * 查找在线用户
     */
    @Query("SELECT u FROM User u WHERE u.status IN ('ONLINE', 'AWAY', 'BUSY')")
    List<User> findOnlineUsers();

    /**
     * 根据昵称模糊搜索用户
     */
    @Query("SELECT u FROM User u WHERE u.nickname LIKE %:keyword% AND u.isActive = true")
    Page<User> searchByNickname(@Param("keyword") String keyword, Pageable pageable);

    /**
     * 更新用户状态
     */
    @Modifying
    @Query("UPDATE User u SET u.status = :status WHERE u.id = :userId")
    int updateUserStatus(@Param("userId") Long userId, @Param("status") UserStatus status);

    /**
     * 更新用户最后登录时间
     */
    @Modifying
    @Query("UPDATE User u SET u.lastLoginAt = :loginTime WHERE u.id = :userId")
    int updateLastLoginTime(@Param("userId") Long userId, @Param("loginTime") LocalDateTime loginTime);

    /**
     * 查找最近注册的用户
     */
    List<User> findTop10ByIsActiveTrueOrderByCreatedAtDesc();

    /**
     * 统计激活用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.isActive = true")
    long countActiveUsers();

    /**
     * 统计在线用户数量
     */
    @Query("SELECT COUNT(u) FROM User u WHERE u.status IN ('ONLINE', 'AWAY', 'BUSY')")
    long countOnlineUsers();
}
