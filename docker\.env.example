# 数据库配置
MYSQL_ROOT_PASSWORD=root123
MYSQL_DATABASE=chatroom
MYSQL_USER=chatroom
MYSQL_PASSWORD=chatroom123

# Redis配置
REDIS_PASSWORD=

# 后端配置
SPRING_PROFILES_ACTIVE=docker
JWT_SECRET=chatroom-jwt-secret-key-2024-very-long-and-secure
JWT_EXPIRATION=604800000

# 文件上传配置
FILE_UPLOAD_PATH=./uploads/
FILE_MAX_SIZE=10485760
FILE_ALLOWED_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,txt

# WebSocket配置
WEBSOCKET_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173

# 网络配置
BACKEND_PORT=8080
FRONTEND_PORT=80
MYSQL_PORT=3306
REDIS_PORT=6379
