package com.chatroom.service;

import com.chatroom.entity.ChatRoom;
import com.chatroom.entity.Message;
import com.chatroom.entity.User;
import com.chatroom.enums.MessageType;
import com.chatroom.repository.ChatRoomRepository;
import com.chatroom.repository.MessageRepository;
import com.chatroom.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;

/**
 * 消息服务类
 */
@Service
@Transactional
public class MessageService {

    private static final Logger logger = LoggerFactory.getLogger(MessageService.class);

    @Autowired
    private MessageRepository messageRepository;

    @Autowired
    private UserRepository userRepository;

    @Autowired
    private ChatRoomRepository chatRoomRepository;

    /**
     * 保存消息
     */
    public Message saveMessage(Long roomId, Long senderId, String content, String type, Long replyToId) {
        logger.info("保存消息: roomId={}, senderId={}, content={}", roomId, senderId, content);

        // 查找房间
        ChatRoom room = chatRoomRepository.findById(roomId)
                .orElseThrow(() -> new RuntimeException("聊天室不存在"));

        // 查找发送者
        User sender = userRepository.findById(senderId)
                .orElseThrow(() -> new RuntimeException("用户不存在"));

        // 创建消息
        Message message = new Message();
        message.setRoom(room);
        message.setSender(sender);
        message.setContent(content);
        message.setType(MessageType.valueOf(type.toUpperCase()));

        // 设置回复消息
        if (replyToId != null) {
            Optional<Message> replyToOpt = messageRepository.findById(replyToId);
            replyToOpt.ifPresent(message::setReplyTo);
        }

        Message savedMessage = messageRepository.save(message);
        logger.info("消息保存成功: messageId={}", savedMessage.getId());
        return savedMessage;
    }

    /**
     * 获取房间消息（分页）
     */
    @Transactional(readOnly = true)
    public Page<Message> getRoomMessages(Long roomId, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        return messageRepository.findByRoomId(roomId, pageable);
    }

    /**
     * 获取房间最近消息
     */
    @Transactional(readOnly = true)
    public List<Message> getRecentMessages(Long roomId, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return messageRepository.findRecentMessages(roomId, pageable);
    }

    /**
     * 搜索消息
     */
    @Transactional(readOnly = true)
    public Page<Message> searchMessages(Long roomId, String keyword, int page, int size) {
        Pageable pageable = PageRequest.of(page, size, Sort.by(Sort.Direction.DESC, "createdAt"));
        return messageRepository.searchMessages(roomId, keyword, pageable);
    }

    /**
     * 撤回消息
     */
    public boolean recallMessage(Long messageId, Long userId) {
        logger.info("撤回消息: messageId={}, userId={}", messageId, userId);

        Optional<Message> messageOpt = messageRepository.findById(messageId);
        if (!messageOpt.isPresent()) {
            logger.error("消息不存在: messageId={}", messageId);
            return false;
        }

        Message message = messageOpt.get();

        // 检查是否是消息发送者
        if (!message.getSender().getId().equals(userId)) {
            logger.error("只有消息发送者才能撤回消息: messageId={}, userId={}", messageId, userId);
            return false;
        }

        // 检查是否在撤回时间限制内
        if (!message.canBeRecalled()) {
            logger.error("消息撤回时间已过: messageId={}", messageId);
            return false;
        }

        // 标记消息为已删除
        message.markAsDeleted();
        messageRepository.save(message);

        logger.info("消息撤回成功: messageId={}", messageId);
        return true;
    }

    /**
     * 获取消息详情
     */
    @Transactional(readOnly = true)
    public Optional<Message> getMessageById(Long messageId) {
        return messageRepository.findById(messageId);
    }

    /**
     * 统计房间消息数量
     */
    @Transactional(readOnly = true)
    public long countRoomMessages(Long roomId) {
        return messageRepository.countRoomMessages(roomId);
    }

    /**
     * 统计用户发送的消息数量
     */
    @Transactional(readOnly = true)
    public long countUserMessages(Long userId) {
        return messageRepository.countUserMessages(userId);
    }

    /**
     * 获取用户在房间的最后一条消息
     */
    @Transactional(readOnly = true)
    public Optional<Message> getUserLastMessageInRoom(Long roomId, Long userId) {
        Pageable pageable = PageRequest.of(0, 1);
        List<Message> messages = messageRepository.findUserLastMessageInRoom(roomId, userId, pageable);
        return messages.isEmpty() ? Optional.empty() : Optional.of(messages.get(0));
    }

    /**
     * 获取消息的回复列表
     */
    @Transactional(readOnly = true)
    public List<Message> getMessageReplies(Long messageId) {
        return messageRepository.findReplies(messageId);
    }

    /**
     * 统计今日消息数量
     */
    @Transactional(readOnly = true)
    public long getTodayMessageCount() {
        return messageRepository.countTodayMessages();
    }
}
