const { Client } = require('@stomp/stompjs');
const SockJS = require('sockjs-client');

// JWT token from registration
const token = 'eyJhbGciOiJIUzUxMiJ9.eyJ1c2VySWQiOjEsInN1YiI6InRlc3R1c2VyIiwiaWF0IjoxNzUwOTUxMTkzLCJleHAiOjE3NTE1NTU5OTN9.GfiS38md2lT41CjdnK5-XzPG8yw7aXcFDGmeB7tDk6nAfsyigjzPLqv4rWPEROZD7-P51gwWqgim-GtpLkG3Xw';

console.log('开始测试WebSocket连接...');

// 创建STOMP客户端
const client = new Client({
  webSocketFactory: () => new SockJS(`http://localhost:8080/api/ws?token=${token}`),
  connectHeaders: {
    Authorization: `Bearer ${token}`
  },
  debug: (str) => {
    console.log('STOMP Debug:', str);
  },
  reconnectDelay: 5000,
  heartbeatIncoming: 4000,
  heartbeatOutgoing: 4000,
});

// 连接成功
client.onConnect = (frame) => {
  console.log('WebSocket连接成功:', frame);
  
  // 订阅房间消息
  client.subscribe('/topic/room/1', (message) => {
    console.log('收到房间消息:', JSON.parse(message.body));
  });
  
  // 发送加入房间消息
  client.publish({
    destination: '/app/chat.addUser/1',
    body: JSON.stringify({
      username: 'testuser',
      nickname: 'testuser',
      type: 'JOIN'
    })
  });
  
  console.log('已加入房间1');
  
  // 5秒后发送一条测试消息
  setTimeout(() => {
    client.publish({
      destination: '/app/chat.sendMessage/1',
      body: JSON.stringify({
        content: 'Hello from WebSocket test!',
        type: 'TEXT',
        username: 'testuser',
        nickname: 'testuser'
      })
    });
    console.log('已发送测试消息');
  }, 5000);
  
  // 10秒后断开连接
  setTimeout(() => {
    client.deactivate();
    console.log('WebSocket连接已断开');
    process.exit(0);
  }, 10000);
};

// 连接断开
client.onDisconnect = (frame) => {
  console.log('WebSocket连接断开:', frame);
};

// 连接错误
client.onStompError = (frame) => {
  console.error('WebSocket连接错误:', frame);
};

// 激活连接
client.activate();

console.log('正在连接WebSocket...');
