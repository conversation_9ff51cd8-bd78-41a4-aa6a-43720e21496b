#!/bin/bash

# 聊天室项目启动脚本

set -e

echo "🚀 启动聊天室项目..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查环境变量文件
if [ ! -f .env ]; then
    if [ -f docker/.env.example ]; then
        echo "📋 复制环境变量配置文件..."
        cp docker/.env.example .env
        echo "✅ 请编辑 .env 文件配置您的环境变量"
    else
        echo "❌ 未找到环境变量配置文件"
        exit 1
    fi
fi

# 创建必要的目录
echo "📁 创建必要的目录..."
mkdir -p uploads
mkdir -p logs

# 构建并启动服务
echo "🔨 构建并启动服务..."
docker-compose up --build -d

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 10

# 检查服务状态
echo "🔍 检查服务状态..."
docker-compose ps

# 显示访问信息
echo ""
echo "🎉 聊天室项目启动成功！"
echo ""
echo "📱 前端应用: http://localhost"
echo "🔧 后端API: http://localhost:8080/api"
echo "📚 API文档: http://localhost:8080/swagger-ui.html"
echo "🗄️  数据库: localhost:3306"
echo "🔴 Redis: localhost:6379"
echo ""
echo "📋 查看日志: docker-compose logs -f"
echo "🛑 停止服务: docker-compose down"
echo ""
