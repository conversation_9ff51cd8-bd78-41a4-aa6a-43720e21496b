# 🚀 实时聊天室系统

一个现代化的实时聊天室应用，采用React前端和Spring Boot后端架构，支持多用户在线聊天、房间管理、文件分享等功能。

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Java](https://img.shields.io/badge/Java-17+-orange.svg)
![React](https://img.shields.io/badge/React-18+-blue.svg)
![Spring Boot](https://img.shields.io/badge/Spring%20Boot-3.x-green.svg)

## ✨ 功能特性

### 🔐 用户管理
- 用户注册/登录（支持用户名或邮箱）
- JWT Token认证
- 个人资料管理（昵称、头像、个性签名）
- 在线状态显示
- 密码修改

### 💬 聊天功能
- 实时消息收发
- 多种消息类型（文本、图片、文件）
- 消息状态显示（发送中、已发送、已读）
- 消息撤回（2分钟内）
- 消息引用回复
- @提及功能
- 表情符号支持

### 🏠 聊天室管理
- 创建/加入聊天室
- 公开房间、私有房间、一对一私聊
- 房间成员管理
- 房间权限控制
- 在线用户列表

### 📚 消息历史
- 消息历史记录
- 消息搜索功能
- 分页加载
- 消息导出

### 🎨 用户体验
- 现代化UI设计
- 响应式布局
- 深色模式支持
- 流畅动画效果
- 移动端适配

## 🏗️ 项目结构

```
chatroom/
├── 📱 frontend/              # React前端应用
│   ├── src/
│   │   ├── components/       # 可复用组件
│   │   ├── pages/           # 页面组件
│   │   ├── services/        # API服务
│   │   ├── store/           # 状态管理
│   │   ├── types/           # TypeScript类型
│   │   └── styles/          # 样式文件
│   ├── public/              # 静态资源
│   └── package.json         # 依赖配置
├── 🔧 backend/              # Spring Boot后端服务
│   ├── src/main/java/com/chatroom/
│   │   ├── controller/      # 控制器
│   │   ├── service/         # 业务逻辑
│   │   ├── repository/      # 数据访问
│   │   ├── entity/          # 实体类
│   │   ├── dto/             # 数据传输对象
│   │   ├── config/          # 配置类
│   │   └── util/            # 工具类
│   └── pom.xml              # Maven配置
├── 🗄️ database/             # 数据库脚本和配置
├── 🐳 docker/               # Docker配置文件
├── 📜 scripts/              # 部署脚本
├── 📖 docs/                 # 项目文档
└── 📋 README.md             # 项目说明
```

## 🛠️ 技术栈

### 前端技术
| 技术 | 版本 | 说明 |
|------|------|------|
| React | 18+ | 主要前端框架 |
| TypeScript | 5.x | 类型安全 |
| Vite | 5.x | 构建工具 |
| Ant Design | 5.x | UI组件库 |
| Tailwind CSS | 3.x | 原子化CSS框架 |
| Zustand | 4.x | 状态管理 |
| React Query | 5.x | 数据获取与缓存 |
| Socket.io | 4.x | WebSocket通信 |
| Framer Motion | 10.x | 动画库 |

### 后端技术
| 技术 | 版本 | 说明 |
|------|------|------|
| Spring Boot | 3.x | 主要后端框架 |
| Spring Security | 6.x | 安全认证 |
| Spring Data JPA | 3.x | 数据访问层 |
| Spring WebSocket | 6.x | WebSocket支持 |
| MySQL | 8.0+ | 主数据库 |
| Redis | 6.0+ | 缓存和会话存储 |
| JWT | - | 身份认证 |
| Maven | 3.8+ | 项目管理 |

## 🚀 快速开始

### 环境要求
- **Node.js** 18+
- **Java** 17+
- **MySQL** 8.0+
- **Redis** 6.0+
- **Docker** 20.10+ (可选)

### 方式一：Docker部署（推荐）

1. **克隆项目**
```bash
git clone <repository-url>
cd chatroom
```

2. **初始化项目**
```bash
make setup
```

3. **启动服务**
```bash
make start
```

4. **访问应用**
- 🌐 前端应用: http://localhost
- 🔧 后端API: http://localhost:8080/api
- 📚 API文档: http://localhost:8080/swagger-ui.html

### 方式二：本地开发

1. **启动数据库服务**
```bash
make db-start
```

2. **启动后端服务**
```bash
cd backend
mvn spring-boot:run
```

3. **启动前端应用**
```bash
cd frontend
npm install
npm run dev
```

4. **访问应用**
- 🌐 前端应用: http://localhost:5173
- 🔧 后端API: http://localhost:8080/api

### 方式三：开发环境一键启动
```bash
make dev
```

## 📋 可用命令

| 命令 | 说明 |
|------|------|
| `make start` | 启动完整项目（Docker） |
| `make stop` | 停止项目 |
| `make dev` | 启动开发环境 |
| `make build` | 构建项目 |
| `make test` | 运行测试 |
| `make clean` | 清理项目 |
| `make logs` | 查看日志 |
| `make setup` | 初始化项目 |

## 📖 文档

- 📘 [开发指南](docs/DEVELOPMENT.md) - 详细的开发环境搭建和开发流程
- 📗 [API文档](docs/API.md) - 完整的API接口文档
- 📙 [部署指南](docs/DEPLOYMENT.md) - 生产环境部署指南

## 🎯 项目演示

### 登录页面
- 现代化的登录界面
- 支持用户名或邮箱登录
- 流畅的动画效果

### 聊天界面
- 实时消息收发
- 直观的用户界面
- 响应式设计

### 功能特性
- 多聊天室支持
- 文件分享
- 用户在线状态
- 消息历史记录

## 🔧 配置说明

### 环境变量
复制 `docker/.env.example` 到 `.env` 并修改相应配置：

```bash
# 数据库配置
MYSQL_ROOT_PASSWORD=your_password
MYSQL_DATABASE=chatroom
MYSQL_USER=chatroom
MYSQL_PASSWORD=your_password

# JWT配置
JWT_SECRET=your_jwt_secret_key
JWT_EXPIRATION=604800000

# 文件上传配置
FILE_MAX_SIZE=10485760
FILE_ALLOWED_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,txt
```

## 🧪 测试

### 运行测试
```bash
# 后端测试
cd backend && mvn test

# 前端测试
cd frontend && npm test

# 或者使用make命令
make test
```

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- [Spring Boot](https://spring.io/projects/spring-boot) - 强大的Java框架
- [React](https://reactjs.org/) - 优秀的前端框架
- [Ant Design](https://ant.design/) - 企业级UI设计语言
- [Tailwind CSS](https://tailwindcss.com/) - 实用的CSS框架

## 📞 联系方式

如果您有任何问题或建议，请通过以下方式联系我们：

- 📧 Email: <EMAIL>
- 🐛 Issues: [GitHub Issues](https://github.com/your-username/chatroom/issues)

---

⭐ 如果这个项目对您有帮助，请给我们一个星标！
