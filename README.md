# 实时聊天室系统

一个现代化的实时聊天室应用，采用React前端和Spring Boot后端架构。

## 项目结构

```
chatroom/
├── frontend/          # React前端应用
├── backend/           # Spring Boot后端服务
├── database/          # 数据库脚本和配置
├── docker/            # Docker配置文件
├── docs/              # 项目文档
└── README.md          # 项目说明
```

## 技术栈

### 前端
- React 18+ + TypeScript
- Vite 构建工具
- Ant Design UI组件库
- Tailwind CSS 样式框架
- Socket.io-client 实时通信
- Zustand 状态管理
- React Query 数据获取

### 后端
- Spring Boot 3.x
- Spring Security 安全认证
- Spring Data JPA 数据访问
- Spring WebSocket 实时通信
- MySQL 主数据库
- Redis 缓存
- JWT 身份认证
- Maven 项目管理

## 快速开始

### 环境要求
- Node.js 18+
- Java 17+
- MySQL 8.0+
- Redis 6.0+
- Docker (可选)

### 本地开发

1. 克隆项目
```bash
git clone <repository-url>
cd chatroom
```

2. 启动后端服务
```bash
cd backend
mvn spring-boot:run
```

3. 启动前端应用
```bash
cd frontend
npm install
npm run dev
```

4. 访问应用
- 前端: http://localhost:5173
- 后端API: http://localhost:8080
- API文档: http://localhost:8080/swagger-ui.html

### Docker部署

```bash
docker-compose up -d
```

## 功能特性

- ✅ 用户注册/登录
- ✅ 实时聊天
- ✅ 聊天室管理
- ✅ 消息历史
- ✅ 文件分享
- ✅ 响应式设计
- ✅ 深色模式

## 开发指南

详细的开发指南请参考 [docs/](./docs/) 目录下的文档。

## 许可证

MIT License
