import React from 'react';
import { Layout, Typography, Button, Space } from 'antd';
import { LogoutOutlined, MessageOutlined } from '@ant-design/icons';
import { motion } from 'framer-motion';
import { useAuthStore } from '@/store/useAuthStore';

const { Header, Content } = Layout;
const { Title, Text } = Typography;

const ChatPage: React.FC = () => {
  const { user, logout } = useAuthStore();

  const handleLogout = () => {
    logout();
  };

  return (
    <Layout className="min-h-screen">
      <Header className="bg-white border-b border-gray-200 px-6 flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center justify-center w-10 h-10 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full">
            <MessageOutlined className="text-white text-lg" />
          </div>
          <Title level={4} className="mb-0">
            聊天室
          </Title>
        </div>
        
        <Space>
          <Text>欢迎, {user?.nickname || user?.username}</Text>
          <Button 
            type="text" 
            icon={<LogoutOutlined />}
            onClick={handleLogout}
          >
            退出
          </Button>
        </Space>
      </Header>

      <Content className="bg-gray-50">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="h-full flex items-center justify-center p-8"
        >
          <div className="text-center max-w-md">
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring", stiffness: 200 }}
              className="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mb-6"
            >
              <MessageOutlined className="text-4xl text-white" />
            </motion.div>
            
            <Title level={2} className="mb-4">
              聊天功能开发中
            </Title>
            
            <Text type="secondary" className="text-lg block mb-6">
              我们正在努力开发完整的聊天功能，包括实时消息、聊天室管理、文件分享等特性。
            </Text>
            
            <div className="bg-white p-6 rounded-lg shadow-sm border">
              <Title level={4} className="mb-3">
                即将推出的功能
              </Title>
              <ul className="text-left space-y-2">
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  实时消息收发
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  多聊天室支持
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  文件和图片分享
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  用户在线状态
                </li>
                <li className="flex items-center">
                  <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                  消息历史记录
                </li>
              </ul>
            </div>
          </div>
        </motion.div>
      </Content>
    </Layout>
  );
};

export default ChatPage;
