package com.chatroom.config;

import com.chatroom.dto.UserStatusDto;
import com.chatroom.entity.User;
import com.chatroom.service.UserService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.simp.SimpMessageSendingOperations;
import org.springframework.messaging.simp.stomp.StompHeaderAccessor;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.SessionConnectedEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;

import java.util.Optional;

/**
 * WebSocket事件监听器
 */
@Component
public class WebSocketEventListener {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketEventListener.class);

    @Autowired
    private SimpMessageSendingOperations messagingTemplate;

    @Autowired
    private UserService userService;

    /**
     * WebSocket连接建立事件
     */
    @EventListener
    public void handleWebSocketConnectListener(SessionConnectedEvent event) {
        logger.info("WebSocket连接建立: sessionId={}", getSessionId(event));
    }

    /**
     * WebSocket连接断开事件
     */
    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(event.getMessage());
        
        String username = (String) headerAccessor.getSessionAttributes().get("username");
        Long roomId = (Long) headerAccessor.getSessionAttributes().get("roomId");
        
        logger.info("WebSocket连接断开: sessionId={}, username={}, roomId={}", 
                   getSessionId(event), username, roomId);

        if (username != null) {
            try {
                // 查找用户
                Optional<User> userOpt = userService.findByUsername(username);
                if (userOpt.isPresent()) {
                    User user = userOpt.get();
                    
                    // 更新用户状态为离线
                    userService.updateUserStatus(user.getId(), com.chatroom.enums.UserStatus.OFFLINE);

                    // 如果用户在某个房间中，广播用户离开消息
                    if (roomId != null) {
                        UserStatusDto statusMessage = new UserStatusDto();
                        statusMessage.setUserId(user.getId());
                        statusMessage.setUsername(username);
                        statusMessage.setNickname(user.getNickname() != null ? user.getNickname() : username);
                        statusMessage.setAvatarUrl(user.getAvatarUrl());
                        statusMessage.setStatus("OFFLINE");
                        statusMessage.setType("LEAVE");

                        // 广播用户离开消息
                        messagingTemplate.convertAndSend("/topic/room/" + roomId + "/users", statusMessage);
                        
                        logger.info("用户离开房间: username={}, roomId={}", username, roomId);
                    }
                }
            } catch (Exception e) {
                logger.error("处理用户断开连接失败: {}", e.getMessage(), e);
            }
        }
    }

    /**
     * 获取会话ID
     */
    private String getSessionId(Object event) {
        if (event instanceof SessionConnectedEvent) {
            StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(((SessionConnectedEvent) event).getMessage());
            return headerAccessor.getSessionId();
        } else if (event instanceof SessionDisconnectEvent) {
            StompHeaderAccessor headerAccessor = StompHeaderAccessor.wrap(((SessionDisconnectEvent) event).getMessage());
            return headerAccessor.getSessionId();
        }
        return "unknown";
    }
}
